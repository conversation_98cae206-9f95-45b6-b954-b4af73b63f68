import 'package:flutter/material.dart';
import 'package:nsl/models/custom_image.dart';
import 'package:nsl/theme/spacing.dart';
import '../utils/font_manager.dart';
import '../theme/app_colors.dart';

class WebCreationFlowMainScreen extends StatefulWidget {
  const WebCreationFlowMainScreen({Key? key}) : super(key: key);

  @override
  State<WebCreationFlowMainScreen> createState() =>
      _WebCreationFlowMainScreenState();
}

class _WebCreationFlowMainScreenState extends State<WebCreationFlowMainScreen> {
  String _selectedTab = 'Recent';
  int _currentPage = 0;
  final int _itemsPerPage = 10;
  String _sortColumn = 'lastOpened';
  bool _sortAscending = false;

  // Search functionality
  final TextEditingController _searchController = TextEditingController();
  bool _showSearchBar = false;

  // Category filter functionality
  String? _selectedCategoryFilter;

  // Static data for the table
  final List<Map<String, dynamic>> _allData = [
    // Role entries
    {
      'fileName': 'CEO',
      'type': 'Role',
      'project': '',
      'solution': '',
      'object': '',
      'role': 'Role',
      'lastOpened': DateTime(2025, 9, 24),
      'isFavorite': true,
    },
    {
      'fileName': 'Product Manager',
      'type': 'Role',
      'project': '',
      'solution': '',
      'object': '',
      'role': 'Role',
      'lastOpened': DateTime(2025, 9, 23),
      'isFavorite': false,
    },
    {
      'fileName': 'Admin',
      'type': 'Role',
      'project': '',
      'solution': '',
      'object': '',
      'role': 'Role',
      'lastOpened': DateTime(2025, 9, 21),
      'isFavorite': false,
    },
    // Object entries
    {
      'fileName': 'Employee',
      'type': 'Object',
      'project': '',
      'solution': '',
      'object': 'Object',
      'role': '',
      'lastOpened': DateTime(2025, 9, 24),
      'isFavorite': false,
    },
    {
      'fileName': 'Order',
      'type': 'Object',
      'project': '',
      'solution': '',
      'object': 'Object',
      'role': '',
      'lastOpened': DateTime(2025, 9, 22),
      'isFavorite': false,
    },
    {
      'fileName': 'Customer',
      'type': 'Object',
      'project': '',
      'solution': '',
      'object': 'Object',
      'role': '',
      'lastOpened': DateTime(2025, 9, 21),
      'isFavorite': true,
    },
    {
      'fileName': 'Product',
      'type': 'Object',
      'project': '',
      'solution': '',
      'object': 'Object',
      'role': '',
      'lastOpened': DateTime(2025, 9, 20),
      'isFavorite': true,
    },
    // Solution entries
    {
      'fileName': 'Customer order product',
      'type': 'Solution',
      'project': '',
      'solution': 'Solution',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 24),
      'isFavorite': false,
    },
    {
      'fileName': 'Inventory System',
      'type': 'Solution',
      'project': '',
      'solution': 'Solution',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 23),
      'isFavorite': true,
    },
    {
      'fileName': 'Sales Dashboard',
      'type': 'Solution',
      'project': '',
      'solution': 'Solution',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 22),
      'isFavorite': false,
    },
    {
      'fileName': 'Payment System',
      'type': 'Solution',
      'project': '',
      'solution': 'Solution',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 20),
      'isFavorite': false,
    },
    // Project entries
    {
      'fileName': 'E-commerce Platform',
      'type': 'Project',
      'project': 'Project',
      'solution': '',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 25),
      'isFavorite': true,
    },
    {
      'fileName': 'CRM System',
      'type': 'Project',
      'project': 'Project',
      'solution': '',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 24),
      'isFavorite': false,
    },
    {
      'fileName': 'Mobile App Development',
      'type': 'Project',
      'project': 'Project',
      'solution': '',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 23),
      'isFavorite': true,
    },
    {
      'fileName': 'Data Analytics Dashboard',
      'type': 'Project',
      'project': 'Project',
      'solution': '',
      'object': '',
      'role': '',
      'lastOpened': DateTime(2025, 9, 22),
      'isFavorite': false,
    },
  ];

  List<Map<String, dynamic>> get _filteredData {
    List<Map<String, dynamic>> filtered = [];

    switch (_selectedTab) {
      case 'Favourite':
        filtered =
            _allData.where((item) => item['isFavorite'] == true).toList();
        break;
      default: // Recent
        filtered = List.from(_allData);
    }

    // Apply category filter
    if (_selectedCategoryFilter != null) {
      filtered = filtered
          .where((item) => item['type'] == _selectedCategoryFilter)
          .toList();
    }

    // Apply search filter
    final searchText = _searchController.text.toLowerCase();
    if (searchText.isNotEmpty) {
      filtered = filtered
          .where((item) =>
              (item['fileName']?.toLowerCase().contains(searchText) ?? false) ||
              (item['type']?.toLowerCase().contains(searchText) ?? false))
          .toList();
    }

    // Sort the data by last opened (most recent first)
    filtered.sort((a, b) {
      DateTime aDate = a['lastOpened'];
      DateTime bDate = b['lastOpened'];
      return bDate.compareTo(aDate);
    });

    return filtered;
  }

  List<Map<String, dynamic>> get _paginatedData {
    final startIndex = _currentPage * _itemsPerPage;
    final endIndex =
        (startIndex + _itemsPerPage).clamp(0, _filteredData.length);
    return _filteredData.sublist(startIndex, endIndex);
  }

  int get _totalPages => (_filteredData.length / _itemsPerPage).ceil();

  void _sortTable(String column) {
    setState(() {
      if (_sortColumn == column) {
        _sortAscending = !_sortAscending;
      } else {
        _sortColumn = column;
        _sortAscending = true;
      }
      _currentPage = 0; // Reset to first page when sorting
    });
  }

  void _showDiscoverModal() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return _buildModal(
          title: 'How Discover Works - Your AI-Guided Journey',
          content: _buildDiscoverModalContent(),
        );
      },
    );
  }

  void _showDevelopModal() {
    showDialog(
      context: context,
      barrierDismissible: true,
      builder: (BuildContext context) {
        return _buildModal(
          title: 'How Development Works - Your Custom Build Journey',
          content: _buildDevelopModalContent(),
        );
      },
    );
  }

  @override
  void initState() {
    super.initState();
    _searchController.addListener(_filterData);
  }

  @override
  void dispose() {
    _searchController.removeListener(_filterData);
    _searchController.dispose();
    super.dispose();
  }

  // Filter data based on search text
  void _filterData() {
    setState(() {
      _currentPage = 0; // Reset to first page when filtering
    });
  }

  // Toggle search bar visibility
  void _toggleSearchBar() {
    setState(() {
      _showSearchBar = !_showSearchBar;
      if (!_showSearchBar) {
        // Clear search when hiding search bar
        _searchController.clear();
      }
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Color(0xffF7F9FB),
      body: Padding(
        padding: const EdgeInsets.all(20.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            CustomTabHeader(),
            const SizedBox(height: 20),
            _buildMainContent(),
            const SizedBox(height: 20),
            _buildBottomSection(),
          ],
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      crossAxisAlignment: CrossAxisAlignment.center,
      children: [
        const SizedBox(), // Left placeholder

        // Center-aligned tabs
        Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            _buildCenterTabItem(
                icon: Icons.library_books, label: 'My Library', isActive: true),
            const SizedBox(width: 16),
            _buildCenterTabItem(icon: Icons.event_note, label: 'Organizer'),
            const SizedBox(width: 16),
            _buildCenterTabItem(icon: Icons.bug_report, label: 'Testing'),
          ],
        ),

        // Right-aligned "More templates"
        Text(
          'More templates',
          style: TextStyle(
            fontFamily: FontManager.fontFamilyTiemposText,
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.textBlue,
            decoration: TextDecoration.underline,
          ),
        ),
      ],
    );
  }

  Widget _buildCenterTabItem(
      {required IconData icon, required String label, bool isActive = false}) {
    return Row(
      children: [
        Icon(
          icon,
          size: 16,
          color: isActive ? AppColors.primaryBlue : AppColors.textGreyColor,
        ),
        const SizedBox(width: 6),
        Text(
          label,
          style: TextStyle(
            fontFamily: FontManager.fontFamilyTiemposText,
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isActive ? AppColors.primaryBlue : AppColors.textGreyColor,
          ),
        ),
      ],
    );
  }

  Widget _buildMainContent() {
    return Center(
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          SizedBox(
            width: MediaQuery.of(context).size.width/4.5,
            child: _buildDiscoverSection(),
          ),
          const SizedBox(width: 24),
          SizedBox(
            width: MediaQuery.of(context).size.width/4.5,
            child: _buildDevelopSection(),
          ),
        ],
      ),
    );
  }

  Widget _buildDiscoverSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        // border: Border.all(color: AppColors.greyBorder),
        borderRadius: BorderRadius.circular(AppSpacing.xxs),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  // color: AppColors.textGreyColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Icon(
                  Icons.search,
                  size: 16,
                  color: AppColors.textGreyColor,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Discover',
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimaryLight,
                ),
              ),
              const Spacer(),
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: _showDiscoverModal,
                  child: Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border:
                          Border.all(color: AppColors.textGreyColor, width: 1),
                    ),
                    child: Center(
                      child: Text(
                        'i',
                        style: TextStyle(
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: AppColors.textGreyColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Share your industry and the solutions you need—our NSL AI will handle complete solution discovery and build it tailored to your needs.',
            style: TextStyle(
              fontFamily: FontManager.fontFamilyTiemposText,
              fontSize: 13,
              fontWeight: FontWeight.w400,
              color: Colors.black,
              height:2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDevelopSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: Colors.white,
        // border: Border.all(color: AppColors.greyBorder),
        borderRadius: BorderRadius.circular(AppSpacing.xxs),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                width: 24,
                height: 24,
                decoration: BoxDecoration(
                  // color: AppColors.textGreyColor.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(4),
                ),
                child: Icon(
                  Icons.code,
                  size: 16,
                  color: AppColors.textGreyColor,
                ),
              ),
              const SizedBox(width: 8),
              Text(
                'Develop',
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textPrimaryLight,
                ),
              ),
              const Spacer(),
              MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: _showDevelopModal,
                  child: Container(
                    width: 16,
                    height: 16,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border:
                          Border.all(color: AppColors.textGreyColor, width: 1),
                    ),
                    child: Center(
                      child: Text(
                        'i',
                        style: TextStyle(
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontSize: 10,
                          fontWeight: FontWeight.w500,
                          color: AppColors.textGreyColor,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            'Enter or upload your requirement, and we\'ll extract, develop, and refine your solution with AI-guided suggestions throughout.',
            style: TextStyle(
              fontFamily: FontManager.fontFamilyTiemposText,
              fontSize: 13,
              fontWeight: FontWeight.w400,
              color: Colors.black,
              height: 2,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBottomSection() {
    return Expanded(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildTabBar(),
          const SizedBox(height: 16),
          Expanded(
            child: _buildTable(),
          ),
          const SizedBox(height: 16),
          _buildPagination(),
        ],
      ),
    );
  }

  Widget _buildTabBar() {
    final tabs = ['Recent', 'Favourite'];

    return Row(
      children: [
        ...tabs.map((tab) => _buildTabItem(tab)),
        const Spacer(),
        _showSearchBar
            ? SearchBarWidget(
                controller: _searchController,
                onClose: _toggleSearchBar,
              )
            : GestureDetector(
                onTap: _toggleSearchBar,
                child: MouseRegion(

                  cursor: SystemMouseCursors.click,
                  child: Container(
                     margin: const EdgeInsets.only(right: AppSpacing.sm),
                    height: 36,
                    child: HoverableSearchIcon(),
                  ),
                ),
              ),
      ],
    );
  }

  Widget _buildTabItem(String tab) {
    final isSelected = _selectedTab == tab;

    return MouseRegion(
      cursor: SystemMouseCursors.click,
      child: GestureDetector(
        onTap: () {
          setState(() {
            _selectedTab = tab;
            _currentPage = 0; // Reset to first page when changing tabs
          });
        },
        child: Container(
          margin: const EdgeInsets.only(right: 8),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          decoration: BoxDecoration(
            color: isSelected ? AppColors.black : Colors.transparent,
            borderRadius: BorderRadius.circular(4),
          ),
          child: Text(
            tab,
            style: TextStyle(
              fontFamily: FontManager.fontFamilyTiemposText,
              fontSize: 14,
              fontWeight: FontWeight.w500,
              color: isSelected ? Colors.white : AppColors.textGreyColor,
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildTable() {
    return Container(
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border.all(color: AppColors.greyBorder),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Column(
        children: [
          _buildTableHeader(),
          Expanded(
            child: ListView.builder(
              itemCount: _paginatedData.length,
              itemBuilder: (context, index) {
                return _buildTableRow(_paginatedData[index], index);
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTableHeader() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          bottom: BorderSide(color: AppColors.greyBorder),
        ),
        borderRadius: const BorderRadius.only(
          topLeft: Radius.circular(8),
          topRight: Radius.circular(8),
        ),
      ),
      child: Row(
        children: [
          // File Name - Left aligned
          Expanded(
            flex: 3,
            child: Align(
              alignment: Alignment.centerLeft,
              child: Text(
                'File Name',
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textGreyColor,
                ),
              ),
            ),
          ),
          // Middle group - Project, Solution, Object, Role
          Expanded(
            flex: 6,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildSortableHeader('Project', 'project'),
                _buildSortableHeader('Solution', 'solution'),
                _buildSortableHeader('Object', 'object'),
                _buildSortableHeader('Role', 'role'),
              ],
            ),
          ),
          // Last Opened - Right aligned
          Expanded(
            flex: 2,
            child: Align(
              alignment: Alignment.centerRight,
              child: Text(
                'Last Opened',
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                  color: AppColors.textGreyColor,
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSortableHeader(String title, String column) {
    // Only allow filtering for category columns
    final isFilterableColumn = ['project', 'solution', 'object', 'role'].contains(column);
    final isCurrentFilter = _selectedCategoryFilter == _getCategoryFromColumn(column);

    return MouseRegion(
      cursor: isFilterableColumn ? SystemMouseCursors.click : SystemMouseCursors.basic,
      child: GestureDetector(
        onTap: isFilterableColumn ? () => _filterByCategory(column) : null,
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              title,
              style: TextStyle(
                fontFamily: FontManager.fontFamilyTiemposText,
                fontSize: 14,
                fontWeight: FontWeight.w600,
                color: isCurrentFilter ? AppColors.textBlue : AppColors.textGreyColor,
              ),
            ),
            if (isFilterableColumn) ...[
              const SizedBox(width: 4),
              Icon(
                isCurrentFilter ? Icons.check : Icons.filter_list,
                size: 16,
                color: isCurrentFilter ? AppColors.textBlue : AppColors.textGreyColor,
              ),
            ],
          ],
        ),
      ),
    );
  }

  String? _getCategoryFromColumn(String column) {
    switch (column) {
      case 'project':
        return 'Project';
      case 'solution':
        return 'Solution';
      case 'object':
        return 'Object';
      case 'role':
        return 'Role';
      default:
        return null;
    }
  }

  void _filterByCategory(String column) {
    final category = _getCategoryFromColumn(column);
    setState(() {
      if (_selectedCategoryFilter == category) {
        // If already selected, clear the filter
        _selectedCategoryFilter = null;
      } else {
        // Set new filter
        _selectedCategoryFilter = category;
      }
      _currentPage = 0; // Reset to first page when filtering
    });
  }

  Widget _buildTableRow(Map<String, dynamic> item, int index) {
    final isEvenRow = index % 2 == 0;
    
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: isEvenRow ? Colors.grey.shade50 : Colors.white,
        border: Border(
          left: BorderSide(color: AppColors.greyBorder, width: 1),
          right: BorderSide(color: AppColors.greyBorder, width: 1),
          bottom: BorderSide(color: AppColors.greyBorder, width: 0.5),
        ),
      ),
      child: Row(
        children: [
          // File Name - Left aligned
          Expanded(
            flex: 3,
            child: Row(
              children: [
                _buildTypeIcon(item['type']),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        item['fileName'],
                        style: TextStyle(
                          fontFamily: FontManager.fontFamilyTiemposText,
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          color: AppColors.textPrimaryLight,
                        ),
                      ),
                      if (item['type'].isNotEmpty)
                        Text(
                          item['type'],
                          style: TextStyle(
                            fontFamily: FontManager.fontFamilyTiemposText,
                            fontSize: 12,
                            fontWeight: FontWeight.w400,
                            color: AppColors.textGreyColor,
                          ),
                        ),
                    ],
                  ),
                ),
              ],
            ),
          ),
          // Middle group - Project, Solution, Object, Role
          Expanded(
            flex: 6,
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                Expanded(
                  child: Center(
                    child: Text(
                      item['project'] ?? '',
                      style: TextStyle(
                        fontFamily: FontManager.fontFamilyTiemposText,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: AppColors.textGreyColor,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Center(
                    child: Text(
                      item['solution'] ?? '',
                      style: TextStyle(
                        fontFamily: FontManager.fontFamilyTiemposText,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: AppColors.textGreyColor,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Center(
                    child: Text(
                      item['object'] ?? '',
                      style: TextStyle(
                        fontFamily: FontManager.fontFamilyTiemposText,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: AppColors.textGreyColor,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Center(
                    child: Text(
                      item['role'] ?? '',
                      style: TextStyle(
                        fontFamily: FontManager.fontFamilyTiemposText,
                        fontSize: 14,
                        fontWeight: FontWeight.w400,
                        color: AppColors.textGreyColor,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
          // Last Opened - Right aligned
          Expanded(
            flex: 2,
            child: Align(
              alignment: Alignment.centerRight,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () {
                        setState(() {
                          item['isFavorite'] = !item['isFavorite'];
                        });
                      },
                      child: Icon(
                        item['isFavorite'] ? Icons.star : Icons.star_border,
                        size: 20,
                        color: item['isFavorite']
                            ? Colors.amber
                            : AppColors.textGreyColor,
                      ),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Text(
                    _formatDate(item['lastOpened']),
                    style: TextStyle(
                      fontFamily: FontManager.fontFamilyTiemposText,
                      fontSize: 14,
                      fontWeight: FontWeight.w400,
                      color: AppColors.textGreyColor,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTypeIcon(String type) {
    IconData iconData;
    Color iconColor = AppColors.textBlue;

    switch (type) {
      case 'Role':
        iconData = Icons.person;
        break;
      case 'Object':
        iconData = Icons.inventory_2;
        break;
      case 'Solution':
        iconData = Icons.lightbulb;
        break;
      case 'Project':
        iconData = Icons.folder_open;
        break;
      default:
        iconData = Icons.description;
    }

    return Container(
      width: 32,
      height: 32,
      decoration: BoxDecoration(
        color: iconColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(4),
      ),
      child: Icon(
        iconData,
        size: 16,
        color: iconColor,
      ),
    );
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date).inDays;

    if (difference == 0) {
      return 'Today';
    } else if (difference == 1) {
      return 'Yesterday, ${date.day}/${date.month}/${date.year}';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }

  Widget _buildPagination() {
    if (_totalPages <= 1) return const SizedBox.shrink();

    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        MouseRegion(
          cursor: _currentPage > 0
              ? SystemMouseCursors.click
              : SystemMouseCursors.basic,
          child: GestureDetector(
            onTap: _currentPage > 0
                ? () {
                    setState(() {
                      _currentPage--;
                    });
                  }
                : null,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.greyBorder),
                borderRadius: BorderRadius.circular(4),
                color: _currentPage > 0 ? Colors.white : AppColors.greyBg,
              ),
              child: Text(
                'Previous',
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: _currentPage > 0
                      ? AppColors.textPrimaryLight
                      : AppColors.textGreyColor,
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Text(
          'Page ${_currentPage + 1} of $_totalPages',
          style: TextStyle(
            fontFamily: FontManager.fontFamilyTiemposText,
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: AppColors.textGreyColor,
          ),
        ),
        const SizedBox(width: 16),
        MouseRegion(
          cursor: _currentPage < _totalPages - 1
              ? SystemMouseCursors.click
              : SystemMouseCursors.basic,
          child: GestureDetector(
            onTap: _currentPage < _totalPages - 1
                ? () {
                    setState(() {
                      _currentPage++;
                    });
                  }
                : null,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
              decoration: BoxDecoration(
                border: Border.all(color: AppColors.greyBorder),
                borderRadius: BorderRadius.circular(4),
                color: _currentPage < _totalPages - 1
                    ? Colors.white
                    : AppColors.greyBg,
              ),
              child: Text(
                'Next',
                style: TextStyle(
                  fontFamily: FontManager.fontFamilyTiemposText,
                  fontSize: 14,
                  fontWeight: FontWeight.w500,
                  color: _currentPage < _totalPages - 1
                      ? AppColors.textPrimaryLight
                      : AppColors.textGreyColor,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildModal({required String title, required Widget content}) {
    return Dialog(
      backgroundColor: Colors.transparent,
      child: Container(
        width: 600,
        constraints: const BoxConstraints(maxHeight: 500),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 20,
              offset: const Offset(0, 10),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Header
            Container(
              padding: const EdgeInsets.all(24),
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(color: AppColors.greyBorder, width: 1),
                ),
              ),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontFamily: FontManager.fontFamilyTiemposText,
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: AppColors.textPrimaryLight,
                      ),
                    ),
                  ),
                  MouseRegion(
                    cursor: SystemMouseCursors.click,
                    child: GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: Icon(
                        Icons.close,
                        size: 24,
                        color: AppColors.textGreyColor,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            // Content
            Flexible(
              child: SingleChildScrollView(
                padding: const EdgeInsets.all(24),
                child: content,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDiscoverModalContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildModalStep(
          'Step 1: Describe Your solution',
          'Specify your business industry, organization size, operational requirements, and geographic locations for the solution.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 2: AI Analysis',
          'Our AI engine automatically discovers and lists out the roles, entities, and workflows for your solution',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 3: Review & Customize',
          'Validate the document in details and customize any components you want to modify with prompt',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 4: Development',
          'Once you have confirmed the final components, proceed with the development of your solution using the discovered framework',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 5: Testing',
          'Test your completed solution to ensure it works as expected.',
        ),
      ],
    );
  }

  Widget _buildDevelopModalContent() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        _buildModalStep(
          'Step 1: Requirements Definition',
          'Craft your detailed solution requirements through our intuitive prompt interface, providing comprehensive information about your business needs.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 2: Intelligent Parsing',
          'Our AI engine intelligently extracts and identifies the essential components from your comprehensive requirements.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 3: Solution Architecture',
          'Seamlessly add new components and fine-tune every aspect of your solution framework to perfectly match your vision.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 4: AI-Powered Optimization',
          'Leverage smart AI recommendations to enhance and optimize your solution design for maximum effectiveness.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 5: Industry Alignment',
          'Effortlessly import industry-standard components and best practices tailored to your business sector.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 6: Solution Validation',
          'Thoroughly review your complete solution framework, including all components and workflow structures.',
        ),
        const SizedBox(height: 16),
        _buildModalStep(
          'Step 7: Implementation',
          '',
        ),
      ],
    );
  }

  Widget _buildModalStep(String title, String description) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            fontFamily: FontManager.fontFamilyTiemposText,
            fontSize: 14,
            fontWeight: FontWeight.w600,
            color: AppColors.textPrimaryLight,
          ),
        ),
        if (description.isNotEmpty) ...[
          const SizedBox(height: 4),
          Text(
            description,
            style: TextStyle(
              fontFamily: FontManager.fontFamilyTiemposText,
              fontSize: 14,
              fontWeight: FontWeight.w400,
              color: AppColors.textGreyColor,
              height: 1.4,
            ),
          ),
        ],
      ],
    );
  }
}

class CustomTabHeader extends StatefulWidget {
  @override
  _CustomTabHeaderState createState() => _CustomTabHeaderState();
}

class _CustomTabHeaderState extends State<CustomTabHeader> {
  String _selectedTab = 'My Library';
  String? _hoveredTab;

  final List<_TabItem> _tabs = [
    _TabItem('My Library', Icons.library_books),
    _TabItem('Organizer', Icons.calendar_today),
    _TabItem('Testing', Icons.settings),
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          // Left Placeholder to balance layout
          const SizedBox(width: 100),

          // Center Tab Buttons
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: _tabs.map((tab) {
              final bool isSelected = tab.label == _selectedTab;
              final bool isHovered = tab.label == _hoveredTab;

              final Color bgColor = isSelected
                  ? Colors.blue
                  : isHovered
                      ? Colors.white
                      : Colors.transparent;

              final Color textColor = isSelected
                  ? Colors.white
                  : isHovered
                      ? Colors.blue
                      : Colors.black;

              final Color iconColor = textColor;

              final BorderSide borderSide = isHovered && !isSelected
                  ? BorderSide(color: Colors.blue)
                  : BorderSide(color: Colors.transparent);

              return MouseRegion(
                onEnter: (_) => setState(() => _hoveredTab = tab.label),
                onExit: (_) => setState(() => _hoveredTab = null),
                child: GestureDetector(
                  onTap: () => setState(() => _selectedTab = tab.label),
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 8),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 14, vertical: 8),
                    decoration: BoxDecoration(
                      color: bgColor,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.fromBorderSide(borderSide),
                    ),
                    child: Row(
                      children: [
                        Icon(tab.icon, size: 16, color: iconColor),
                        const SizedBox(width: 6),
                        Text(
                          tab.label,
                          style: TextStyle(
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                            fontFamily: 'TiemposText',
                            color: textColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),

          // Right-aligned "More templates" link
          MouseRegion(
            cursor: SystemMouseCursors.click,
            child: GestureDetector(
              onTap: () {
                // Handle link tap
              },
              child: MouseRegion(
                cursor: SystemMouseCursors.click,
                child: GestureDetector(
                  onTap: () {
                    // Handle link tap
                  },
                  child: Stack(
                    alignment: Alignment.bottomLeft,
                    children: [
                      Text(
                        'More templates',
                        style: TextStyle(
                          fontSize: 14,
                          fontWeight: FontWeight.w500,
                          fontFamily: 'TiemposText',
                          color: Colors.blue,
                          decoration:
                              TextDecoration.none, // disable default underline
                        ),
                      ),
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: Container(
                          height: 1,
                          color: Colors.blue, // underline color
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class _TabItem {
  final String label;
  final IconData icon;

  _TabItem(this.label, this.icon);
}

/// Search bar widget that appears when search icon is clicked
class SearchBarWidget extends StatelessWidget {
  final TextEditingController controller;
  final VoidCallback onClose;

  const SearchBarWidget({
    super.key,
    required this.controller,
    required this.onClose,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      width: 250,
      height: 36,
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(4),
        border: Border.all(color: Colors.grey.shade300),
      ),
      child: Row(
        children: [
          // Search input field
          Expanded(
            child: TextField(
              controller: controller,
              decoration: InputDecoration(
                hintText: 'Search',
                hintStyle: FontManager.getCustomStyle(
                    color: Colors.grey.shade500,
                    fontSize: FontManager.s14,
                    fontFamily: FontManager.fontFamilyTiemposText),
                border: InputBorder.none,
                enabledBorder: InputBorder.none,
                focusedBorder: InputBorder.none,
                contentPadding:
                    const EdgeInsets.symmetric(horizontal: 8.0, vertical: 0),
                isDense: true,
                hoverColor: Colors.transparent,
              ),
              style: FontManager.getCustomStyle(fontSize: FontManager.s14),
              // Auto-focus when search bar appears
              autofocus: true,
            ),
          ),

          // Close button
          GestureDetector(
            onTap: onClose,
            child: MouseRegion(
              cursor: SystemMouseCursors.click,
              child: Padding(
                padding: const EdgeInsets.only(right: 8.0),
                child: Icon(Icons.close, size: 18, color: Colors.grey.shade600),
              ),
            ),
          ),
        ],
      ),
    );
  }
}

class HoverableSearchIcon extends StatefulWidget {
  const HoverableSearchIcon({super.key});

  @override
  _HoverableSearchIconState createState() => _HoverableSearchIconState();
}

class _HoverableSearchIconState extends State<HoverableSearchIcon> {
  bool _isHovered = false;

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => setState(() => _isHovered = true),
      onExit: (_) => setState(() => _isHovered = false),
      cursor: SystemMouseCursors.click,
      child: CustomImage.asset(
        'assets/images/my_business/search_collection.svg',
        width: 20,
        height: 20,
        fit: BoxFit.contain,
        color: _isHovered ? Colors.blue : null, // <-- Change color on hover
      ).toWidget(),
    );
  }
}

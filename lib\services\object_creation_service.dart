import 'package:dio/dio.dart';
import '../models/object_creation_model.dart';
import 'base_api_service.dart';
import 'auth_service.dart';
import '../utils/logger.dart';

/// Service for handling object creation/extraction API operations with session-based API
class ObjectCreationService extends BaseApiService {
  // Base URL for the new extraction API (configurable for different environments)
  // Default: localhost:8630 (as per API documentation)
  // Alternative: **********:8630 (for network deployment)
  static const String _baseUrl = 'http://**********:8630';
  static const String _apiVersion = '/api/v1';

  // Alternative base URL for network deployment (uncomment if needed)
  // static const String _baseUrl = 'http://**********:8630';

  // Auth service for getting user data
  final AuthService _authService = AuthService();

  // Configuration constants
  static const int _maxRetries = 3;
  static const int _retryDelayMs = 1000; // Fixed 1 second delay

  /// Execute an operation with retry logic
  Future<T> _executeWithRetry<T>(
    Future<T> Function() operation, {
    String? operationName,
    int maxRetries = _maxRetries,
  }) async {
    int attempt = 0;

    while (attempt <= maxRetries) {
      try {
        if (attempt > 0) {
          Logger.info(
              '${operationName ?? 'Operation'} retry attempt $attempt/$maxRetries');
        }
        return await operation();
      } catch (e) {
        attempt++;

        // Check if we should retry this error
        bool shouldRetry = _shouldRetryError(e);

        if (attempt > maxRetries || !shouldRetry) {
          Logger.error(
              '${operationName ?? 'Operation'} failed after $attempt attempts: $e');
          rethrow;
        }

        // Fixed delay instead of exponential backoff
        Logger.warning(
            '${operationName ?? 'Operation'} failed (attempt $attempt), retrying in ${_retryDelayMs}ms: $e');
        await Future.delayed(Duration(milliseconds: _retryDelayMs));
      }
    }

    throw Exception(
        '${operationName ?? 'Operation'} failed after $maxRetries retries');
  }

  /// Determine if an error should trigger a retry
  bool _shouldRetryError(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
        case DioExceptionType.connectionError:
          return true;
        case DioExceptionType.badResponse:
          // Retry on server errors (5xx) but not client errors (4xx)
          final statusCode = error.response?.statusCode;
          return statusCode != null && statusCode >= 500;
        default:
          return false;
      }
    }
    return false;
  }

  /// Get user-friendly error message
  String getUserFriendlyMessage(dynamic error) {
    if (error is DioException) {
      switch (error.type) {
        case DioExceptionType.connectionTimeout:
        case DioExceptionType.sendTimeout:
        case DioExceptionType.receiveTimeout:
          return 'Request timed out. Please check your connection and try again.';
        case DioExceptionType.connectionError:
          return 'Unable to connect to the server. Please check your internet connection.';
        case DioExceptionType.badResponse:
          final statusCode = error.response?.statusCode;
          if (statusCode == 401) {
            return 'Authentication failed. Please log in again.';
          } else if (statusCode == 403) {
            return 'You do not have permission to perform this action.';
          } else if (statusCode == 404) {
            return 'The requested resource was not found.';
          } else if (statusCode != null && statusCode >= 500) {
            return 'Server error occurred. Please try again later.';
          }
          return error.response?.data?['message'] ?? 'Request failed';
        default:
          return 'An unexpected error occurred. Please try again.';
      }
    }
    return error.toString();
  }

  /// Make API call using Dio
  Future<Response> _makeApiCall({
    required String method,
    required String url,
    Map<String, dynamic>? data,
    Options? options,
  }) async {
    final dio = Dio();

    switch (method.toUpperCase()) {
      case 'GET':
        return await dio.get(url, options: options);
      case 'POST':
        return await dio.post(url, data: data, options: options);
      case 'PUT':
        return await dio.put(url, data: data, options: options);
      case 'DELETE':
        return await dio.delete(url, options: options);
      default:
        throw Exception('Unsupported HTTP method: $method');
    }
  }

  /// Create a new session for entity extraction
  Future<ObjectCreationResponse> createSession({
    required String originalIntent,
  }) async {
    return await _executeWithRetry<ObjectCreationResponse>(
      () async {
        Logger.info('Creating new session with intent: $originalIntent');

        final endpoint = '$_apiVersion/sessions';
        final fullUrl = '$_baseUrl$endpoint';
        final token = await _authService.getValidToken();

        final options = Options(
          headers: {
            'Content-Type': 'application/json',
            if (token != null) 'Authorization': 'Bearer $token',
          },
        );

        final requestData = {
          'original_intent': originalIntent,
        };

        // final response = await _makeApiCall(
        //   method: 'POST',
        //   url: fullUrl,
        //   data: requestData,
        //   options: options,
        // );
        final response = Response(
          data: {
            "success": true,
            "session_id": "56c3f775-5b8e-4214-9af3-0a267012e4fe",
            "message": "Session created successfully"
          },
          statusCode: 200,
          statusMessage: 'OK',
          headers: Headers(),
          requestOptions: RequestOptions(path: fullUrl),
          isRedirect: false,
          redirects: [],
          extra: null,
        );

        Logger.info('Session creation response: ${response.statusCode}');
        Logger.info('Session creation response data: ${response.data}');

        if (response.statusCode == 200 || response.statusCode == 201) {
          final sessionId = response.data?['session_id'] ??
              response.data?['id'] ??
              response.data?['sessionId'];

          if (sessionId != null) {
            Logger.info('Session created successfully with ID: $sessionId');
            return ObjectCreationResponse(
              success: true,
              message: 'Session created successfully',
              sessionId: sessionId.toString(),
            );
          } else {
            Logger.error('Session created but no session ID found in response');
            Logger.error('Response data keys: ${response.data?.keys.toList()}');
            throw Exception('Session created but no session ID returned');
          }
        } else {
          Logger.error(
              'Session creation failed: ${response.statusCode} - ${response.statusMessage}');
          Logger.error('Error response data: ${response.data}');
          throw Exception(
              'HTTP ${response.statusCode}: ${response.statusMessage}');
        }
      },
      operationName: 'createSession',
    );
  }

  /// Execute comprehensive extraction for a session
  Future<ObjectCreationResponse> executeComprehensiveExtraction({
    required String sessionId,
    required String intent,
    required String tenantName,
    required String businessDomain,
    required String context,
  }) async {
    return await _executeWithRetry<ObjectCreationResponse>(
      () async {
        Logger.info(
            'Executing comprehensive extraction for session: $sessionId');

        // Use the correct API endpoint format: /api/v1/extract/comprehensive/{sessionId}
        final endpoint = '$_apiVersion/extract/comprehensive/$sessionId';
        final fullUrl = '$_baseUrl$endpoint';
        final token = await _authService.getValidToken();

        final options = Options(
          headers: {
            'Content-Type': 'application/json',
            if (token != null) 'Authorization': 'Bearer $token',
          },
        );

        // Use the exact request body format as specified in the API documentation
        final requestData = {
          'intent': intent,
          'tenant_name': tenantName,
          'business_domain': businessDomain,
          'context': context,
        };

        Logger.info('Making comprehensive extraction request to: $fullUrl');
        Logger.info('Request data: $requestData');

        // final response = await _makeApiCall(
        //   method: 'POST',
        //   url: fullUrl,
        //   data: requestData,
        //   options: options,
        // );

        final response = Response(
          data: {
            "success": true,
            "message": "Comprehensive extraction completed successfully",
            "session_id": "56c3f775-5b8e-4214-9af3-0a267012e4fe",
            "data": {
              "total_entities": 12,
              "total_roles": 4,
              "total_go": 2,
              "total_lo": 4,
              "total_attributes": 161,
              "total_business_rules": 0,
              "total_permissions": 8,
              "has_extractions": true,
              "last_updated": "2025-07-22T09:22:13.551821"
            }
          },
          statusCode: 200,
          statusMessage: 'OK',
          headers: Headers(),
          requestOptions: RequestOptions(
              path: '$_baseUrl$_apiVersion/extract/comprehensive/$sessionId'),
          isRedirect: false,
          redirects: [],
          extra: null,
        );

        Logger.info(
            'Comprehensive extraction response: ${response.statusCode}');
        Logger.info('Response data: ${response.data}');

        if (response.statusCode == 200 || response.statusCode == 201) {
          // Parse the response according to API documentation
          if (response.data != null && response.data is Map<String, dynamic>) {
            final responseMap = response.data as Map<String, dynamic>;
            final success = responseMap['success'] ?? false;
            final message = responseMap['message']?.toString() ??
                'Comprehensive extraction completed';
            final returnedSessionId =
                responseMap['session_id']?.toString() ?? sessionId;

            if (success) {
              // Parse the data section with statistics
              final data = responseMap['data'];
              if (data != null && data is Map<String, dynamic>) {
                final totalEntities = data['total_entities']?.toInt() ?? 0;
                final totalRoles = data['total_roles']?.toInt() ?? 0;
                final totalGo = data['total_go']?.toInt() ?? 0;
                final totalLo = data['total_lo']?.toInt() ?? 0;
                final hasExtractions = data['has_extractions'] ?? false;
                final lastUpdated = data['last_updated']?.toString();

                Logger.info('Comprehensive extraction statistics:');
                Logger.info('  - Total entities: $totalEntities');
                Logger.info('  - Total roles: $totalRoles');
                Logger.info('  - Total GO: $totalGo');
                Logger.info('  - Total LO: $totalLo');
                Logger.info('  - Has extractions: $hasExtractions');
                Logger.info('  - Last updated: $lastUpdated');
              }

              Logger.info('Comprehensive extraction completed successfully');
              return ObjectCreationResponse(
                success: true,
                message: message,
                sessionId: returnedSessionId,
              );
            } else {
              throw Exception(message);
            }
          } else {
            // Unexpected response format - throw error instead of fallback
            Logger.error(
                'Unexpected response format from comprehensive extraction API');
            Logger.error('Response data: ${response.data}');
            throw Exception('API returned unexpected response format');
          }
        } else {
          Logger.error(
              'Comprehensive extraction failed: ${response.statusCode} - ${response.statusMessage}');
          Logger.error('Error response data: ${response.data}');
          throw Exception(
              'HTTP ${response.statusCode}: ${response.statusMessage}');
        }
      },
      operationName: 'executeComprehensiveExtraction',
    );
  }

  /// Get entities for a specific session
  Future<ObjectCreationResponse> getSessionEntities(String sessionId) async {
    return await _executeWithRetry<ObjectCreationResponse>(
      () async {
        Logger.info('Fetching entities for session: $sessionId');

        final endpoint = '$_apiVersion/sessions/$sessionId/entities';
        final fullUrl = '$_baseUrl$endpoint';
        final token = await _authService.getValidToken();

        final options = Options(
          headers: {
            'Content-Type': 'application/json',
            if (token != null) 'Authorization': 'Bearer $token',
          },
        );

        // final response = await _makeApiCall(
        //   method: 'GET',
        //   url: fullUrl,
        //   options: options,
        // );

        final response = Response(
          data: {
            "success": true,
            "session_id": "56c3f775-5b8e-4214-9af3-0a267012e4fe",
            "entities": [
              {
                "id": "patient",
                "name": "Patient",
                "displayName": "Patient",
                "type": "master",
                "description":
                    "Central entity storing all patient demographic and identification information",
                "businessPurpose":
                    "Primary record for patient identification and management",
                "businessDomain": "Healthcare",
                "category": "core",
                "tags": ["master", "core"],
                "archivalStrategy": "archive_only",
                "colorTheme": "indigo",
                "icon": "database",
                "attributes": [
                  {
                    "id": "created_by",
                    "name": "created_by",
                    "displayName": "Created By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter created by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter created by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_by",
                    "name": "updated_by",
                    "displayName": "Updated By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter updated by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter updated by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "is_deleted",
                    "name": "is_deleted",
                    "displayName": "Is Deleted",
                    "dataType": "boolean",
                    "required": true,
                    "unique": false,
                    "defaultType": "static_value",
                    "defaultValue": "false",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter is deleted",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "checkbox",
                      "displayFormat": "",
                      "placeholder": "Enter is deleted",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "deleted_at",
                    "name": "deleted_at",
                    "displayName": "Deleted At",
                    "dataType": "datetime",
                    "required": false,
                    "unique": false,
                    "defaultType": "null",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter deleted at",
                    "enumValues": [],
                    "validation": {"required": false, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter deleted at",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "id",
                    "name": "id",
                    "displayName": "Patient ID",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "auto_generated",
                    "defaultValue": "",
                    "description": "Unique patient identifier",
                    "businessPurpose": "",
                    "helperText": "Enter id",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": true,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter id",
                      "readOnly": true,
                      "helpText": "Unique patient identifier",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "mrn",
                    "name": "mrn",
                    "displayName": "Medical Record Number",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "auto_generated",
                    "defaultValue": "",
                    "description": "Medical Record Number",
                    "businessPurpose": "",
                    "helperText": "Enter mrn",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": true,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter mrn",
                      "readOnly": false,
                      "helpText": "Medical Record Number",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "first_name",
                    "name": "first_name",
                    "displayName": "First Name",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Patient's legal first name",
                    "businessPurpose": "",
                    "helperText": "Enter first name",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter first name",
                      "readOnly": false,
                      "helpText": "Patient's legal first name",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "confidential",
                      "piiType": "name",
                      "encryptionRequired": true,
                      "maskingRequired": true,
                      "accessLevel": "read_restricted"
                    }
                  },
                  {
                    "id": "last_name",
                    "name": "last_name",
                    "displayName": "Last Name",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Patient's legal last name",
                    "businessPurpose": "",
                    "helperText": "Enter last name",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter last name",
                      "readOnly": false,
                      "helpText": "Patient's legal last name",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "confidential",
                      "piiType": "name",
                      "encryptionRequired": true,
                      "maskingRequired": true,
                      "accessLevel": "read_restricted"
                    }
                  },
                  {
                    "id": "date_of_birth",
                    "name": "date_of_birth",
                    "displayName": "Date of Birth",
                    "dataType": "date",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Patient's date of birth",
                    "businessPurpose": "",
                    "helperText": "Enter date of birth",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "minDate": "1900-01-01",
                      "maxDate": "2100-12-31"
                    },
                    "uiProperties": {
                      "controlType": "date_picker",
                      "displayFormat": "DD/MM/YYYY",
                      "placeholder": "Enter date of birth",
                      "readOnly": false,
                      "helpText": "Patient's date of birth",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "confidential",
                      "piiType": "date_of_birth",
                      "encryptionRequired": true,
                      "maskingRequired": true,
                      "accessLevel": "read_restricted"
                    }
                  },
                  {
                    "id": "gender",
                    "name": "gender",
                    "displayName": "Gender",
                    "dataType": "enum",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Patient's gender",
                    "businessPurpose": "",
                    "helperText": "Enter gender",
                    "enumValues": ["M", "F", "O", "U"],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "dropdown",
                      "displayFormat": "",
                      "placeholder": "Enter gender",
                      "readOnly": false,
                      "helpText": "Patient's gender",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "status",
                    "name": "status",
                    "displayName": "Status",
                    "dataType": "enum",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "active",
                    "description": "Patient status",
                    "businessPurpose": "",
                    "helperText": "Enter status",
                    "enumValues": ["active", "inactive", "deceased"],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "dropdown",
                      "displayFormat": "",
                      "placeholder": "Enter status",
                      "readOnly": false,
                      "helpText": "Patient status",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "created_date",
                    "name": "created_date",
                    "displayName": "Created Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Record creation timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter created date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter created date",
                      "readOnly": true,
                      "helpText": "Record creation timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_date",
                    "name": "updated_date",
                    "displayName": "Updated Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Last update timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter updated date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter updated date",
                      "readOnly": true,
                      "helpText": "Last update timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  }
                ],
                "relationships": [
                  {
                    "primaryEntity": "",
                    "relatedEntity": "Address",
                    "primaryKey": "",
                    "foreignKey": "patient_id",
                    "relationshipType": "one-to-many",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description": "Patient's addresses",
                    "isRequired": true,
                    "cascadeOptions": {
                      "delete": "restrict",
                      "update": "cascade"
                    }
                  },
                  {
                    "primaryEntity": "",
                    "relatedEntity": "Contact",
                    "primaryKey": "",
                    "foreignKey": "patient_id",
                    "relationshipType": "one-to-many",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description": "Patient's contact information",
                    "isRequired": true,
                    "cascadeOptions": {
                      "delete": "restrict",
                      "update": "cascade"
                    }
                  },
                  {
                    "primaryEntity": "",
                    "relatedEntity": "Insurance",
                    "primaryKey": "",
                    "foreignKey": "patient_id",
                    "relationshipType": "one-to-many",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description": "Patient's insurance policies",
                    "isRequired": false,
                    "cascadeOptions": {
                      "delete": "restrict",
                      "update": "cascade"
                    }
                  }
                ],
                "businessRules": [],
                "enumValues": [],
                "securityClassification": [
                  {
                    "entityAttribute": "Patient.id",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Patient.mrn",
                    "classification": "confidential",
                    "piiType": "medical_id",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "authorized_only",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Patient.first_name",
                    "classification": "confidential",
                    "piiType": "name",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "authorized_only",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Patient.last_name",
                    "classification": "confidential",
                    "piiType": "name",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "authorized_only",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Patient.date_of_birth",
                    "classification": "confidential",
                    "piiType": "date_of_birth",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "authorized_only",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Patient.gender",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Patient.status",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Patient.created_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Patient.updated_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  }
                ],
                "systemPermissions": [
                  {
                    "permissionId": "PERM_ENTITY_PATIENT",
                    "permissionName": "Patient Entity Access",
                    "permissionType": "entity",
                    "resourceIdentifier": "Patient",
                    "actions": ["create", "read", "update", "delete"],
                    "description": "Full access to Patient entity",
                    "scope": "tenant_records",
                    "naturalLanguage": "Permission to manage Patient records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "roleSystemPermissions": [
                  {
                    "roleId": "ROLE_ADMIN",
                    "permissionId": "PERM_ENTITY_PATIENT",
                    "grantedActions": ["create", "read", "update", "delete"],
                    "rowLevelConditions": {"access_level": "tenant_records"},
                    "naturalLanguage":
                        "Admin has full access to Patient records",
                    "version": 1,
                    "status": "active"
                  },
                  {
                    "roleId": "ROLE_USER",
                    "permissionId": "PERM_ENTITY_PATIENT",
                    "grantedActions": ["read"],
                    "rowLevelConditions": {"access_level": "own_records_only"},
                    "naturalLanguage":
                        "User can read their own Patient records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "uiProperties": [
                  {
                    "entityAttribute": "Entity.id",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter id",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Patient ID",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.mrn",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter mrn",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Medical Record Number",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.first_name",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter first name",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "First Name",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.last_name",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter last name",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Last Name",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.date_of_birth",
                    "controlType": "date_picker",
                    "displayFormat": "DD/MM/YYYY",
                    "inputMask": "DD/MM/YYYY",
                    "placeholderText": "Enter date of birth",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Date of Birth",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.gender",
                    "controlType": "dropdown",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter gender",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Gender",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.status",
                    "controlType": "dropdown",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter status",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Status",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.created_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter created date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Created Date",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.updated_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter updated date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Updated Date",
                    "requiredIndicator": true
                  }
                ],
                "confidence": 0.95,
                "extractionMethod": "AI_Comprehensive",
                "lastUpdated": "2025-07-22T14:52:13.543534",
                "configurationStatus": {
                  "completionPercentage": 37.5,
                  "completedSections": 3,
                  "totalSections": 8,
                  "status": "incomplete"
                },
                "completionScore": 75
              },
              {
                "id": "address",
                "name": "Address",
                "displayName": "Address",
                "type": "master",
                "description":
                    "Stores residential and mailing addresses for patients",
                "businessPurpose":
                    "Required for patient contact and documentation",
                "businessDomain": "Healthcare",
                "category": "supporting",
                "tags": ["master"],
                "archivalStrategy": "archive_only",
                "colorTheme": "indigo",
                "icon": "map-pin",
                "attributes": [
                  {
                    "id": "is_deleted",
                    "name": "is_deleted",
                    "displayName": "Is Deleted",
                    "dataType": "boolean",
                    "required": true,
                    "unique": false,
                    "defaultType": "static_value",
                    "defaultValue": "false",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter is deleted",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "checkbox",
                      "displayFormat": "",
                      "placeholder": "Enter is deleted",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "deleted_at",
                    "name": "deleted_at",
                    "displayName": "Deleted At",
                    "dataType": "datetime",
                    "required": false,
                    "unique": false,
                    "defaultType": "null",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter deleted at",
                    "enumValues": [],
                    "validation": {"required": false, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter deleted at",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "id",
                    "name": "id",
                    "displayName": "Address ID",
                    "dataType": "uuid",
                    "required": true,
                    "unique": true,
                    "defaultType": "auto_generated",
                    "defaultValue": "",
                    "description": "Unique identifier for address record",
                    "businessPurpose": "",
                    "helperText": "Enter id",
                    "enumValues": [],
                    "validation": {"required": true, "unique": true},
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter id",
                      "readOnly": true,
                      "helpText": "Unique identifier for address record",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "address_type_id",
                    "name": "address_type_id",
                    "displayName": "Address Type",
                    "dataType": "uuid",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description":
                        "Reference to address type (residential, mailing, etc.)",
                    "businessPurpose": "",
                    "helperText": "Enter address type id",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter address type id",
                      "readOnly": false,
                      "helpText":
                          "Reference to address type (residential, mailing, etc.)",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "confidential",
                      "piiType": "address",
                      "encryptionRequired": true,
                      "maskingRequired": true,
                      "accessLevel": "read_restricted"
                    }
                  },
                  {
                    "id": "street_line1",
                    "name": "street_line1",
                    "displayName": "Street Address Line 1",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Primary street address",
                    "businessPurpose": "",
                    "helperText": "Enter street line1",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter street line1",
                      "readOnly": false,
                      "helpText": "Primary street address",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "street_line2",
                    "name": "street_line2",
                    "displayName": "Street Address Line 2",
                    "dataType": "string",
                    "required": false,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Secondary address information",
                    "businessPurpose": "",
                    "helperText": "Enter street line2",
                    "enumValues": [],
                    "validation": {
                      "required": false,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 0
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter street line2",
                      "readOnly": false,
                      "helpText": "Secondary address information",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "city",
                    "name": "city",
                    "displayName": "City",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "City name",
                    "businessPurpose": "",
                    "helperText": "Enter city",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter city",
                      "readOnly": false,
                      "helpText": "City name",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "state_code",
                    "name": "state_code",
                    "displayName": "State",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Two-letter state code",
                    "businessPurpose": "",
                    "helperText": "Enter state code",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter state code",
                      "readOnly": false,
                      "helpText": "Two-letter state code",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "postal_code",
                    "name": "postal_code",
                    "displayName": "Postal Code",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "ZIP/Postal code",
                    "businessPurpose": "",
                    "helperText": "Enter postal code",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter postal code",
                      "readOnly": false,
                      "helpText": "ZIP/Postal code",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "country_code",
                    "name": "country_code",
                    "displayName": "Country Code",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "US",
                    "description": "ISO country code",
                    "businessPurpose": "",
                    "helperText": "Enter country code",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter country code",
                      "readOnly": false,
                      "helpText": "ISO country code",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "is_primary",
                    "name": "is_primary",
                    "displayName": "Is Primary Address",
                    "dataType": "boolean",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": false,
                    "description": "Indicates if this is the primary address",
                    "businessPurpose": "",
                    "helperText": "Enter is primary",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "checkbox",
                      "displayFormat": "",
                      "placeholder": "Enter is primary",
                      "readOnly": false,
                      "helpText": "Indicates if this is the primary address",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "status",
                    "name": "status",
                    "displayName": "Status",
                    "dataType": "enum",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "active",
                    "description": "Address record status",
                    "businessPurpose": "",
                    "helperText": "Enter status",
                    "enumValues": ["active", "inactive", "archived"],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "dropdown",
                      "displayFormat": "",
                      "placeholder": "Enter status",
                      "readOnly": false,
                      "helpText": "Address record status",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "created_date",
                    "name": "created_date",
                    "displayName": "Created Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Record creation timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter created date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter created date",
                      "readOnly": true,
                      "helpText": "Record creation timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "created_by",
                    "name": "created_by",
                    "displayName": "Created By",
                    "dataType": "uuid",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "User who created the record",
                    "businessPurpose": "",
                    "helperText": "Enter created by",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter created by",
                      "readOnly": false,
                      "helpText": "User who created the record",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_date",
                    "name": "updated_date",
                    "displayName": "Updated Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Last update timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter updated date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter updated date",
                      "readOnly": true,
                      "helpText": "Last update timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_by",
                    "name": "updated_by",
                    "displayName": "Updated By",
                    "dataType": "uuid",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "User who last updated the record",
                    "businessPurpose": "",
                    "helperText": "Enter updated by",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter updated by",
                      "readOnly": false,
                      "helpText": "User who last updated the record",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  }
                ],
                "relationships": [
                  {
                    "primaryEntity": "",
                    "relatedEntity": "Patient",
                    "primaryKey": "",
                    "foreignKey": "patient_id",
                    "relationshipType": "many-to-one",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description": "Address belongs to a patient",
                    "isRequired": true,
                    "cascadeOptions": {
                      "delete": "restrict",
                      "update": "cascade"
                    }
                  },
                  {
                    "primaryEntity": "",
                    "relatedEntity": "AddressType",
                    "primaryKey": "",
                    "foreignKey": "address_type_id",
                    "relationshipType": "many-to-one",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description": "Type of address (residential, mailing)",
                    "isRequired": true,
                    "cascadeOptions": {
                      "delete": "restrict",
                      "update": "cascade"
                    }
                  }
                ],
                "businessRules": [],
                "enumValues": [],
                "securityClassification": [
                  {
                    "entityAttribute": "Address.id",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Address.address_type_id",
                    "classification": "public",
                    "piiType": "address",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": true,
                    "maskingPattern": "*** *** ***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Address.street_line1",
                    "classification": "confidential",
                    "piiType": "address",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "restricted",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Address.street_line2",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Address.city",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Address.state_code",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Address.postal_code",
                    "classification": "confidential",
                    "piiType": "demographic",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "restricted",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Address.country_code",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Address.is_primary",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Address.status",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Address.created_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Address.created_by",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Address.updated_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Address.updated_by",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  }
                ],
                "systemPermissions": [
                  {
                    "permissionId": "PERM_ENTITY_ADDRESS",
                    "permissionName": "Address Entity Access",
                    "permissionType": "entity",
                    "resourceIdentifier": "Address",
                    "actions": ["create", "read", "update", "delete"],
                    "description": "Full access to Address entity",
                    "scope": "tenant_records",
                    "naturalLanguage": "Permission to manage Address records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "roleSystemPermissions": [
                  {
                    "roleId": "ROLE_ADMIN",
                    "permissionId": "PERM_ENTITY_ADDRESS",
                    "grantedActions": ["create", "read", "update", "delete"],
                    "rowLevelConditions": {"access_level": "tenant_records"},
                    "naturalLanguage":
                        "Admin has full access to Address records",
                    "version": 1,
                    "status": "active"
                  },
                  {
                    "roleId": "ROLE_USER",
                    "permissionId": "PERM_ENTITY_ADDRESS",
                    "grantedActions": ["read"],
                    "rowLevelConditions": {"access_level": "own_records_only"},
                    "naturalLanguage":
                        "User can read their own Address records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "uiProperties": [
                  {
                    "entityAttribute": "Entity.id",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter id",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Address ID",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.address_type_id",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter address type id",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Address Type",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.street_line1",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter street line1",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Street Address Line 1",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.street_line2",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter street line2",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Street Address Line 2",
                    "requiredIndicator": false
                  },
                  {
                    "entityAttribute": "Entity.city",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter city",
                    "autoComplete": true,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "City",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.state_code",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter state code",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "State",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.postal_code",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "#####-####",
                    "placeholderText": "Enter postal code",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Postal Code",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.country_code",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter country code",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Country Code",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.is_primary",
                    "controlType": "checkbox",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter is primary",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Is Primary Address",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.status",
                    "controlType": "dropdown",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter status",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Status",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.created_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter created date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Created Date",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.created_by",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter created by",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Created By",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.updated_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter updated date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Updated Date",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.updated_by",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter updated by",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Updated By",
                    "requiredIndicator": true
                  }
                ],
                "confidence": 0.95,
                "extractionMethod": "AI_Comprehensive",
                "lastUpdated": "2025-07-22T14:52:13.543929",
                "configurationStatus": {
                  "completionPercentage": 37.5,
                  "completedSections": 3,
                  "totalSections": 8,
                  "status": "incomplete"
                },
                "completionScore": 75
              },
              {
                "id": "addresstype",
                "name": "AddressType",
                "displayName": "Addresstype",
                "type": "reference",
                "description":
                    "Lookup table for different types of addresses (residential, mailing)",
                "businessPurpose": "Standardization of address classifications",
                "businessDomain": "Healthcare",
                "category": "reference",
                "tags": ["reference"],
                "archivalStrategy": "archive_only",
                "colorTheme": "orange",
                "icon": "map-pin",
                "attributes": [
                  {
                    "id": "created_by",
                    "name": "created_by",
                    "displayName": "Created By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter created by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter created by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_by",
                    "name": "updated_by",
                    "displayName": "Updated By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter updated by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter updated by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "is_deleted",
                    "name": "is_deleted",
                    "displayName": "Is Deleted",
                    "dataType": "boolean",
                    "required": true,
                    "unique": false,
                    "defaultType": "static_value",
                    "defaultValue": "false",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter is deleted",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "checkbox",
                      "displayFormat": "",
                      "placeholder": "Enter is deleted",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "deleted_at",
                    "name": "deleted_at",
                    "displayName": "Deleted At",
                    "dataType": "datetime",
                    "required": false,
                    "unique": false,
                    "defaultType": "null",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter deleted at",
                    "enumValues": [],
                    "validation": {"required": false, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter deleted at",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "id",
                    "name": "id",
                    "displayName": "Address Type ID",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "auto_generated",
                    "defaultValue": "",
                    "description": "Unique identifier for address type",
                    "businessPurpose": "",
                    "helperText": "Enter id",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": true,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter id",
                      "readOnly": true,
                      "helpText": "Unique identifier for address type",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "code",
                    "name": "code",
                    "displayName": "Type Code",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "manual_entry",
                    "defaultValue": "",
                    "description":
                        "Short code for address type (e.g., RES, MAIL)",
                    "businessPurpose": "",
                    "helperText": "Enter code",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": true,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter code",
                      "readOnly": false,
                      "helpText":
                          "Short code for address type (e.g., RES, MAIL)",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "name",
                    "name": "name",
                    "displayName": "Type Name",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "manual_entry",
                    "defaultValue": "",
                    "description":
                        "Full name of address type (e.g., Residential, Mailing)",
                    "businessPurpose": "",
                    "helperText": "Enter name",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": true,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter name",
                      "readOnly": false,
                      "helpText":
                          "Full name of address type (e.g., Residential, Mailing)",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "confidential",
                      "piiType": "name",
                      "encryptionRequired": true,
                      "maskingRequired": true,
                      "accessLevel": "read_restricted"
                    }
                  },
                  {
                    "id": "description",
                    "name": "description",
                    "displayName": "Description",
                    "dataType": "string",
                    "required": false,
                    "unique": false,
                    "defaultType": "manual_entry",
                    "defaultValue": "",
                    "description": "Detailed description of address type usage",
                    "businessPurpose": "",
                    "helperText": "Enter description",
                    "enumValues": [],
                    "validation": {
                      "required": false,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 0
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter description",
                      "readOnly": false,
                      "helpText": "Detailed description of address type usage",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "created_date",
                    "name": "created_date",
                    "displayName": "Created Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Record creation timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter created date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter created date",
                      "readOnly": true,
                      "helpText": "Record creation timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_date",
                    "name": "updated_date",
                    "displayName": "Updated Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Last update timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter updated date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter updated date",
                      "readOnly": true,
                      "helpText": "Last update timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "status",
                    "name": "status",
                    "displayName": "Status",
                    "dataType": "enum",
                    "required": true,
                    "unique": false,
                    "defaultType": "static_value",
                    "defaultValue": "active",
                    "description": "Record status",
                    "businessPurpose": "",
                    "helperText": "Enter status",
                    "enumValues": ["active", "inactive", "deleted"],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "dropdown",
                      "displayFormat": "",
                      "placeholder": "Enter status",
                      "readOnly": false,
                      "helpText": "Record status",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  }
                ],
                "relationships": [
                  {
                    "primaryEntity": "",
                    "relatedEntity": "Address",
                    "primaryKey": "",
                    "foreignKey": "address_type_id",
                    "relationshipType": "one-to-many",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description":
                        "One address type can be associated with multiple addresses",
                    "isRequired": true,
                    "cascadeOptions": {
                      "delete": "restrict",
                      "update": "cascade"
                    }
                  }
                ],
                "businessRules": [],
                "enumValues": [],
                "securityClassification": [
                  {
                    "entityAttribute": "AddressType.id",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "AddressType.code",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "AddressType.name",
                    "classification": "public",
                    "piiType": "name",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "AddressType.description",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "AddressType.created_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "AddressType.updated_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "AddressType.status",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  }
                ],
                "systemPermissions": [
                  {
                    "permissionId": "PERM_ENTITY_ADDRESSTYPE",
                    "permissionName": "AddressType Entity Access",
                    "permissionType": "entity",
                    "resourceIdentifier": "AddressType",
                    "actions": ["create", "read", "update", "delete"],
                    "description": "Full access to AddressType entity",
                    "scope": "tenant_records",
                    "naturalLanguage":
                        "Permission to manage AddressType records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "roleSystemPermissions": [
                  {
                    "roleId": "ROLE_ADMIN",
                    "permissionId": "PERM_ENTITY_ADDRESSTYPE",
                    "grantedActions": ["create", "read", "update", "delete"],
                    "rowLevelConditions": {"access_level": "tenant_records"},
                    "naturalLanguage":
                        "Admin has full access to AddressType records",
                    "version": 1,
                    "status": "active"
                  },
                  {
                    "roleId": "ROLE_USER",
                    "permissionId": "PERM_ENTITY_ADDRESSTYPE",
                    "grantedActions": ["read"],
                    "rowLevelConditions": {"access_level": "own_records_only"},
                    "naturalLanguage":
                        "User can read their own AddressType records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "uiProperties": [
                  {
                    "entityAttribute": "Entity.id",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter id",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Address Type ID",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.code",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter code",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Type Code",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.name",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter name",
                    "autoComplete": true,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Type Name",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.description",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter description",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Description",
                    "requiredIndicator": false
                  },
                  {
                    "entityAttribute": "Entity.created_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter created date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Created Date",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.updated_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter updated date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Updated Date",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.status",
                    "controlType": "dropdown",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter status",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Status",
                    "requiredIndicator": true
                  }
                ],
                "confidence": 0.95,
                "extractionMethod": "AI_Comprehensive",
                "lastUpdated": "2025-07-22T14:52:13.544596",
                "configurationStatus": {
                  "completionPercentage": 37.5,
                  "completedSections": 3,
                  "totalSections": 8,
                  "status": "incomplete"
                },
                "completionScore": 75
              },
              {
                "id": "contact",
                "name": "Contact",
                "displayName": "Contact",
                "type": "master",
                "description":
                    "Stores multiple contact numbers and emergency contacts",
                "businessPurpose": "Essential for patient communication",
                "businessDomain": "Healthcare",
                "category": "supporting",
                "tags": ["master"],
                "archivalStrategy": "archive_only",
                "colorTheme": "indigo",
                "icon": "database",
                "attributes": [
                  {
                    "id": "updated_date",
                    "name": "updated_date",
                    "displayName": "Updated Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter updated date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter updated date",
                      "readOnly": true,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "created_by",
                    "name": "created_by",
                    "displayName": "Created By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter created by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter created by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_by",
                    "name": "updated_by",
                    "displayName": "Updated By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter updated by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter updated by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "is_deleted",
                    "name": "is_deleted",
                    "displayName": "Is Deleted",
                    "dataType": "boolean",
                    "required": true,
                    "unique": false,
                    "defaultType": "static_value",
                    "defaultValue": "false",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter is deleted",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "checkbox",
                      "displayFormat": "",
                      "placeholder": "Enter is deleted",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "deleted_at",
                    "name": "deleted_at",
                    "displayName": "Deleted At",
                    "dataType": "datetime",
                    "required": false,
                    "unique": false,
                    "defaultType": "null",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter deleted at",
                    "enumValues": [],
                    "validation": {"required": false, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter deleted at",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "id",
                    "name": "id",
                    "displayName": "Contact ID",
                    "dataType": "uuid",
                    "required": true,
                    "unique": true,
                    "defaultType": "auto_generated",
                    "defaultValue": "",
                    "description": "Unique identifier for contact record",
                    "businessPurpose": "",
                    "helperText": "Enter id",
                    "enumValues": [],
                    "validation": {"required": true, "unique": true},
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter id",
                      "readOnly": true,
                      "helpText": "Unique identifier for contact record",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "contact_type_id",
                    "name": "contact_type_id",
                    "displayName": "Contact Type",
                    "dataType": "uuid",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description":
                        "Reference to contact type (primary, emergency, work, etc.)",
                    "businessPurpose": "",
                    "helperText": "Enter contact type id",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter contact type id",
                      "readOnly": false,
                      "helpText":
                          "Reference to contact type (primary, emergency, work, etc.)",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "phone_number",
                    "name": "phone_number",
                    "displayName": "Phone Number",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Contact phone number in E.164 format",
                    "businessPurpose": "",
                    "helperText": "Enter phone number",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1,
                      "pattern": "phone"
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter phone number",
                      "readOnly": false,
                      "helpText": "Contact phone number in E.164 format",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "confidential",
                      "piiType": "phone",
                      "encryptionRequired": true,
                      "maskingRequired": true,
                      "accessLevel": "read_restricted"
                    }
                  },
                  {
                    "id": "contact_name",
                    "name": "contact_name",
                    "displayName": "Contact Name",
                    "dataType": "string",
                    "required": false,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description":
                        "Name of emergency contact person if applicable",
                    "businessPurpose": "",
                    "helperText": "Enter contact name",
                    "enumValues": [],
                    "validation": {
                      "required": false,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 0
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter contact name",
                      "readOnly": false,
                      "helpText":
                          "Name of emergency contact person if applicable",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "confidential",
                      "piiType": "name",
                      "encryptionRequired": true,
                      "maskingRequired": true,
                      "accessLevel": "read_restricted"
                    }
                  },
                  {
                    "id": "relationship",
                    "name": "relationship",
                    "displayName": "Relationship",
                    "dataType": "string",
                    "required": false,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description":
                        "Relationship to patient for emergency contacts",
                    "businessPurpose": "",
                    "helperText": "Enter relationship",
                    "enumValues": [],
                    "validation": {
                      "required": false,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 0
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter relationship",
                      "readOnly": false,
                      "helpText":
                          "Relationship to patient for emergency contacts",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "priority_order",
                    "name": "priority_order",
                    "displayName": "Priority",
                    "dataType": "integer",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": 1,
                    "description": "Contact priority order",
                    "businessPurpose": "",
                    "helperText": "Enter priority order",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "min": 0,
                      "max": 999999999
                    },
                    "uiProperties": {
                      "controlType": "number_input",
                      "displayFormat": "#,##0",
                      "placeholder": "Enter priority order",
                      "readOnly": false,
                      "helpText": "Contact priority order",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "created_date",
                    "name": "created_date",
                    "displayName": "Created Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Record creation timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter created date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter created date",
                      "readOnly": true,
                      "helpText": "Record creation timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "status",
                    "name": "status",
                    "displayName": "Status",
                    "dataType": "enum",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "active",
                    "description": "Contact record status",
                    "businessPurpose": "",
                    "helperText": "Enter status",
                    "enumValues": ["active", "inactive", "deleted"],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "dropdown",
                      "displayFormat": "",
                      "placeholder": "Enter status",
                      "readOnly": false,
                      "helpText": "Contact record status",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  }
                ],
                "relationships": [
                  {
                    "primaryEntity": "",
                    "relatedEntity": "Patient",
                    "primaryKey": "",
                    "foreignKey": "patient_id",
                    "relationshipType": "many-to-one",
                    "onDelete": "cascade",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description": "Patient associated with this contact",
                    "isRequired": true,
                    "cascadeOptions": {"delete": "cascade", "update": "cascade"}
                  },
                  {
                    "primaryEntity": "",
                    "relatedEntity": "ContactType",
                    "primaryKey": "",
                    "foreignKey": "contact_type_id",
                    "relationshipType": "many-to-one",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description": "Type of contact (primary, emergency, etc.)",
                    "isRequired": true,
                    "cascadeOptions": {
                      "delete": "restrict",
                      "update": "cascade"
                    }
                  }
                ],
                "businessRules": [],
                "enumValues": [],
                "securityClassification": [
                  {
                    "entityAttribute": "Contact.id",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Contact.contact_type_id",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Contact.phone_number",
                    "classification": "confidential",
                    "piiType": "contact_info",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***-***-####",
                    "accessLevel": "authorized_only",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Contact.contact_name",
                    "classification": "confidential",
                    "piiType": "personal_info",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "authorized_only",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Contact.relationship",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Contact.priority_order",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Contact.created_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Contact.status",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  }
                ],
                "systemPermissions": [
                  {
                    "permissionId": "PERM_ENTITY_CONTACT",
                    "permissionName": "Contact Entity Access",
                    "permissionType": "entity",
                    "resourceIdentifier": "Contact",
                    "actions": ["create", "read", "update", "delete"],
                    "description": "Full access to Contact entity",
                    "scope": "tenant_records",
                    "naturalLanguage": "Permission to manage Contact records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "roleSystemPermissions": [
                  {
                    "roleId": "ROLE_ADMIN",
                    "permissionId": "PERM_ENTITY_CONTACT",
                    "grantedActions": ["create", "read", "update", "delete"],
                    "rowLevelConditions": {"access_level": "tenant_records"},
                    "naturalLanguage":
                        "Admin has full access to Contact records",
                    "version": 1,
                    "status": "active"
                  },
                  {
                    "roleId": "ROLE_USER",
                    "permissionId": "PERM_ENTITY_CONTACT",
                    "grantedActions": ["read"],
                    "rowLevelConditions": {"access_level": "own_records_only"},
                    "naturalLanguage":
                        "User can read their own Contact records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "uiProperties": [
                  {
                    "entityAttribute": "Entity.id",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter id",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Contact ID",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.contact_type_id",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter contact type id",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Contact Type",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.phone_number",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "+## (###) ###-####",
                    "placeholderText": "Enter phone number",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Phone Number",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.contact_name",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter contact name",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Contact Name",
                    "requiredIndicator": false
                  },
                  {
                    "entityAttribute": "Entity.relationship",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter relationship",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Relationship",
                    "requiredIndicator": false
                  },
                  {
                    "entityAttribute": "Entity.priority_order",
                    "controlType": "number_input",
                    "displayFormat": "#,##0",
                    "inputMask": "",
                    "placeholderText": "Enter priority order",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Priority",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.created_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter created date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Created Date",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.status",
                    "controlType": "dropdown",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter status",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Status",
                    "requiredIndicator": true
                  }
                ],
                "confidence": 0.95,
                "extractionMethod": "AI_Comprehensive",
                "lastUpdated": "2025-07-22T14:52:13.544867",
                "configurationStatus": {
                  "completionPercentage": 37.5,
                  "completedSections": 3,
                  "totalSections": 8,
                  "status": "incomplete"
                },
                "completionScore": 75
              },
              {
                "id": "contacttype",
                "name": "ContactType",
                "displayName": "Contacttype",
                "type": "reference",
                "description":
                    "Defines types of contact numbers (primary, secondary, emergency)",
                "businessPurpose": "Categorization of contact information",
                "businessDomain": "Healthcare",
                "category": "reference",
                "tags": ["reference"],
                "archivalStrategy": "archive_only",
                "colorTheme": "orange",
                "icon": "database",
                "attributes": [
                  {
                    "id": "created_by",
                    "name": "created_by",
                    "displayName": "Created By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter created by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter created by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_by",
                    "name": "updated_by",
                    "displayName": "Updated By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter updated by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter updated by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "is_deleted",
                    "name": "is_deleted",
                    "displayName": "Is Deleted",
                    "dataType": "boolean",
                    "required": true,
                    "unique": false,
                    "defaultType": "static_value",
                    "defaultValue": "false",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter is deleted",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "checkbox",
                      "displayFormat": "",
                      "placeholder": "Enter is deleted",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "deleted_at",
                    "name": "deleted_at",
                    "displayName": "Deleted At",
                    "dataType": "datetime",
                    "required": false,
                    "unique": false,
                    "defaultType": "null",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter deleted at",
                    "enumValues": [],
                    "validation": {"required": false, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter deleted at",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "id",
                    "name": "id",
                    "displayName": "Contact Type ID",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "auto_generated",
                    "defaultValue": "",
                    "description": "Unique identifier for contact type",
                    "businessPurpose": "",
                    "helperText": "Enter id",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": true,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter id",
                      "readOnly": true,
                      "helpText": "Unique identifier for contact type",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "code",
                    "name": "code",
                    "displayName": "Contact Type Code",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "manual_entry",
                    "defaultValue": "",
                    "description":
                        "Unique code for contact type (e.g., PRIMARY, SECONDARY, EMERGENCY)",
                    "businessPurpose": "",
                    "helperText": "Enter code",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": true,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter code",
                      "readOnly": false,
                      "helpText":
                          "Unique code for contact type (e.g., PRIMARY, SECONDARY, EMERGENCY)",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "name",
                    "name": "name",
                    "displayName": "Contact Type Name",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "manual_entry",
                    "defaultValue": "",
                    "description": "Display name for contact type",
                    "businessPurpose": "",
                    "helperText": "Enter name",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": true,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter name",
                      "readOnly": false,
                      "helpText": "Display name for contact type",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "confidential",
                      "piiType": "name",
                      "encryptionRequired": true,
                      "maskingRequired": true,
                      "accessLevel": "read_restricted"
                    }
                  },
                  {
                    "id": "description",
                    "name": "description",
                    "displayName": "Description",
                    "dataType": "string",
                    "required": false,
                    "unique": false,
                    "defaultType": "manual_entry",
                    "defaultValue": "",
                    "description": "Detailed description of contact type",
                    "businessPurpose": "",
                    "helperText": "Enter description",
                    "enumValues": [],
                    "validation": {
                      "required": false,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 0
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter description",
                      "readOnly": false,
                      "helpText": "Detailed description of contact type",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "priority_order",
                    "name": "priority_order",
                    "displayName": "Priority Order",
                    "dataType": "integer",
                    "required": true,
                    "unique": true,
                    "defaultType": "manual_entry",
                    "defaultValue": "",
                    "description": "Display/processing order of contact types",
                    "businessPurpose": "",
                    "helperText": "Enter priority order",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": true,
                      "min": 0,
                      "max": 999999999
                    },
                    "uiProperties": {
                      "controlType": "number_input",
                      "displayFormat": "#,##0",
                      "placeholder": "Enter priority order",
                      "readOnly": false,
                      "helpText": "Display/processing order of contact types",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "created_date",
                    "name": "created_date",
                    "displayName": "Created Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Record creation timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter created date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter created date",
                      "readOnly": true,
                      "helpText": "Record creation timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_date",
                    "name": "updated_date",
                    "displayName": "Updated Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Last update timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter updated date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter updated date",
                      "readOnly": true,
                      "helpText": "Last update timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "status",
                    "name": "status",
                    "displayName": "Status",
                    "dataType": "enum",
                    "required": true,
                    "unique": false,
                    "defaultType": "static_value",
                    "defaultValue": "active",
                    "description": "Record status",
                    "businessPurpose": "",
                    "helperText": "Enter status",
                    "enumValues": ["active", "inactive", "deleted"],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "dropdown",
                      "displayFormat": "",
                      "placeholder": "Enter status",
                      "readOnly": false,
                      "helpText": "Record status",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  }
                ],
                "relationships": [
                  {
                    "primaryEntity": "",
                    "relatedEntity": "PatientContact",
                    "primaryKey": "",
                    "foreignKey": "contact_type_id",
                    "relationshipType": "one-to-many",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description":
                        "Links contact types to patient contact information",
                    "isRequired": true,
                    "cascadeOptions": {
                      "delete": "restrict",
                      "update": "cascade"
                    }
                  }
                ],
                "businessRules": [],
                "enumValues": [],
                "securityClassification": [
                  {
                    "entityAttribute": "ContactType.id",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "ContactType.code",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "ContactType.name",
                    "classification": "public",
                    "piiType": "name",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "ContactType.description",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "ContactType.priority_order",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "ContactType.created_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "ContactType.updated_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "ContactType.status",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  }
                ],
                "systemPermissions": [
                  {
                    "permissionId": "PERM_ENTITY_CONTACTTYPE",
                    "permissionName": "ContactType Entity Access",
                    "permissionType": "entity",
                    "resourceIdentifier": "ContactType",
                    "actions": ["create", "read", "update", "delete"],
                    "description": "Full access to ContactType entity",
                    "scope": "tenant_records",
                    "naturalLanguage":
                        "Permission to manage ContactType records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "roleSystemPermissions": [
                  {
                    "roleId": "ROLE_ADMIN",
                    "permissionId": "PERM_ENTITY_CONTACTTYPE",
                    "grantedActions": ["create", "read", "update", "delete"],
                    "rowLevelConditions": {"access_level": "tenant_records"},
                    "naturalLanguage":
                        "Admin has full access to ContactType records",
                    "version": 1,
                    "status": "active"
                  },
                  {
                    "roleId": "ROLE_USER",
                    "permissionId": "PERM_ENTITY_CONTACTTYPE",
                    "grantedActions": ["read"],
                    "rowLevelConditions": {"access_level": "own_records_only"},
                    "naturalLanguage":
                        "User can read their own ContactType records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "uiProperties": [
                  {
                    "entityAttribute": "Entity.id",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter id",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Contact Type ID",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.code",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter code",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Contact Type Code",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.name",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter name",
                    "autoComplete": true,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Contact Type Name",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.description",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter description",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Description",
                    "requiredIndicator": false
                  },
                  {
                    "entityAttribute": "Entity.priority_order",
                    "controlType": "number_input",
                    "displayFormat": "#,##0",
                    "inputMask": "",
                    "placeholderText": "Enter priority order",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Priority Order",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.created_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter created date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Created Date",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.updated_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter updated date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Updated Date",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.status",
                    "controlType": "dropdown",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter status",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Status",
                    "requiredIndicator": true
                  }
                ],
                "confidence": 0.95,
                "extractionMethod": "AI_Comprehensive",
                "lastUpdated": "2025-07-22T14:52:13.545112",
                "configurationStatus": {
                  "completionPercentage": 37.5,
                  "completedSections": 3,
                  "totalSections": 8,
                  "status": "incomplete"
                },
                "completionScore": 75
              },
              {
                "id": "insurance",
                "name": "Insurance",
                "displayName": "Insurance",
                "type": "master",
                "description":
                    "Stores patient insurance information and coverage details",
                "businessPurpose":
                    "Required for billing and coverage verification",
                "businessDomain": "Healthcare",
                "category": "core",
                "tags": ["master", "core"],
                "archivalStrategy": "archive_only",
                "colorTheme": "indigo",
                "icon": "database",
                "attributes": [
                  {
                    "id": "created_by",
                    "name": "created_by",
                    "displayName": "Created By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter created by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter created by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_by",
                    "name": "updated_by",
                    "displayName": "Updated By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter updated by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter updated by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "is_deleted",
                    "name": "is_deleted",
                    "displayName": "Is Deleted",
                    "dataType": "boolean",
                    "required": true,
                    "unique": false,
                    "defaultType": "static_value",
                    "defaultValue": "false",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter is deleted",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "checkbox",
                      "displayFormat": "",
                      "placeholder": "Enter is deleted",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "deleted_at",
                    "name": "deleted_at",
                    "displayName": "Deleted At",
                    "dataType": "datetime",
                    "required": false,
                    "unique": false,
                    "defaultType": "null",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter deleted at",
                    "enumValues": [],
                    "validation": {"required": false, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter deleted at",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "id",
                    "name": "id",
                    "displayName": "Insurance ID",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "auto_generated",
                    "defaultValue": "",
                    "description": "Unique identifier for insurance record",
                    "businessPurpose": "",
                    "helperText": "Enter id",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": true,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter id",
                      "readOnly": true,
                      "helpText": "Unique identifier for insurance record",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "patient_id",
                    "name": "patient_id",
                    "displayName": "Patient ID",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Reference to associated patient",
                    "businessPurpose": "",
                    "helperText": "Enter patient id",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter patient id",
                      "readOnly": false,
                      "helpText": "Reference to associated patient",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "provider_id",
                    "name": "provider_id",
                    "displayName": "Insurance Provider ID",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Reference to insurance provider",
                    "businessPurpose": "",
                    "helperText": "Enter provider id",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter provider id",
                      "readOnly": false,
                      "helpText": "Reference to insurance provider",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "policy_number",
                    "name": "policy_number",
                    "displayName": "Policy Number",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Insurance policy number",
                    "businessPurpose": "",
                    "helperText": "Enter policy number",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": true,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter policy number",
                      "readOnly": false,
                      "helpText": "Insurance policy number",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "group_number",
                    "name": "group_number",
                    "displayName": "Group Number",
                    "dataType": "string",
                    "required": false,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Insurance group number",
                    "businessPurpose": "",
                    "helperText": "Enter group number",
                    "enumValues": [],
                    "validation": {
                      "required": false,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 0
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter group number",
                      "readOnly": false,
                      "helpText": "Insurance group number",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "coverage_start_date",
                    "name": "coverage_start_date",
                    "displayName": "Coverage Start Date",
                    "dataType": "date",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Start date of insurance coverage",
                    "businessPurpose": "",
                    "helperText": "Enter coverage start date",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "minDate": "1900-01-01",
                      "maxDate": "2100-12-31"
                    },
                    "uiProperties": {
                      "controlType": "date_picker",
                      "displayFormat": "DD/MM/YYYY",
                      "placeholder": "Enter coverage start date",
                      "readOnly": false,
                      "helpText": "Start date of insurance coverage",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "date_of_birth",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "coverage_end_date",
                    "name": "coverage_end_date",
                    "displayName": "Coverage End Date",
                    "dataType": "date",
                    "required": false,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "End date of insurance coverage",
                    "businessPurpose": "",
                    "helperText": "Enter coverage end date",
                    "enumValues": [],
                    "validation": {
                      "required": false,
                      "unique": false,
                      "minDate": "1900-01-01",
                      "maxDate": "2100-12-31"
                    },
                    "uiProperties": {
                      "controlType": "date_picker",
                      "displayFormat": "DD/MM/YYYY",
                      "placeholder": "Enter coverage end date",
                      "readOnly": false,
                      "helpText": "End date of insurance coverage",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "date_of_birth",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "is_primary",
                    "name": "is_primary",
                    "displayName": "Is Primary Insurance",
                    "dataType": "boolean",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": false,
                    "description": "Indicates if this is primary insurance",
                    "businessPurpose": "",
                    "helperText": "Enter is primary",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "checkbox",
                      "displayFormat": "",
                      "placeholder": "Enter is primary",
                      "readOnly": false,
                      "helpText": "Indicates if this is primary insurance",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "status",
                    "name": "status",
                    "displayName": "Status",
                    "dataType": "enum",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "active",
                    "description": "Insurance status",
                    "businessPurpose": "",
                    "helperText": "Enter status",
                    "enumValues": ["active", "inactive", "expired"],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "dropdown",
                      "displayFormat": "",
                      "placeholder": "Enter status",
                      "readOnly": false,
                      "helpText": "Insurance status",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "created_date",
                    "name": "created_date",
                    "displayName": "Created Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Record creation timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter created date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter created date",
                      "readOnly": true,
                      "helpText": "Record creation timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_date",
                    "name": "updated_date",
                    "displayName": "Updated Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Last update timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter updated date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter updated date",
                      "readOnly": true,
                      "helpText": "Last update timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  }
                ],
                "relationships": [
                  {
                    "primaryEntity": "",
                    "relatedEntity": "Patient",
                    "primaryKey": "",
                    "foreignKey": "patient_id",
                    "relationshipType": "many-to-one",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description": "Insurance belongs to a patient",
                    "isRequired": true,
                    "cascadeOptions": {
                      "delete": "restrict",
                      "update": "cascade"
                    }
                  },
                  {
                    "primaryEntity": "",
                    "relatedEntity": "InsuranceProvider",
                    "primaryKey": "",
                    "foreignKey": "provider_id",
                    "relationshipType": "many-to-one",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description":
                        "Insurance is provided by an insurance company",
                    "isRequired": true,
                    "cascadeOptions": {
                      "delete": "restrict",
                      "update": "cascade"
                    }
                  }
                ],
                "businessRules": [],
                "enumValues": [],
                "securityClassification": [
                  {
                    "entityAttribute": "Insurance.id",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Insurance.patient_id",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["hipaa"]
                  },
                  {
                    "entityAttribute": "Insurance.provider_id",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Insurance.policy_number",
                    "classification": "confidential",
                    "piiType": "financial",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "restricted",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Insurance.group_number",
                    "classification": "confidential",
                    "piiType": "financial",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "restricted",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Insurance.coverage_start_date",
                    "classification": "public",
                    "piiType": "date_of_birth",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Insurance.coverage_end_date",
                    "classification": "public",
                    "piiType": "date_of_birth",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Insurance.is_primary",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Insurance.status",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Insurance.created_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "Insurance.updated_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  }
                ],
                "systemPermissions": [
                  {
                    "permissionId": "PERM_ENTITY_INSURANCE",
                    "permissionName": "Insurance Entity Access",
                    "permissionType": "entity",
                    "resourceIdentifier": "Insurance",
                    "actions": ["create", "read", "update", "delete"],
                    "description": "Full access to Insurance entity",
                    "scope": "tenant_records",
                    "naturalLanguage": "Permission to manage Insurance records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "roleSystemPermissions": [
                  {
                    "roleId": "ROLE_ADMIN",
                    "permissionId": "PERM_ENTITY_INSURANCE",
                    "grantedActions": ["create", "read", "update", "delete"],
                    "rowLevelConditions": {"access_level": "tenant_records"},
                    "naturalLanguage":
                        "Admin has full access to Insurance records",
                    "version": 1,
                    "status": "active"
                  },
                  {
                    "roleId": "ROLE_USER",
                    "permissionId": "PERM_ENTITY_INSURANCE",
                    "grantedActions": ["read"],
                    "rowLevelConditions": {"access_level": "own_records_only"},
                    "naturalLanguage":
                        "User can read their own Insurance records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "uiProperties": [
                  {
                    "entityAttribute": "Entity.id",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter id",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Insurance ID",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.patient_id",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter patient id",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Patient ID",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.provider_id",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter provider id",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Insurance Provider ID",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.policy_number",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter policy number",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Policy Number",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.group_number",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter group number",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Group Number",
                    "requiredIndicator": false
                  },
                  {
                    "entityAttribute": "Entity.coverage_start_date",
                    "controlType": "date_picker",
                    "displayFormat": "DD/MM/YYYY",
                    "inputMask": "DD/MM/YYYY",
                    "placeholderText": "Enter coverage start date",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Coverage Start Date",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.coverage_end_date",
                    "controlType": "date_picker",
                    "displayFormat": "DD/MM/YYYY",
                    "inputMask": "DD/MM/YYYY",
                    "placeholderText": "Enter coverage end date",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Coverage End Date",
                    "requiredIndicator": false
                  },
                  {
                    "entityAttribute": "Entity.is_primary",
                    "controlType": "checkbox",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter is primary",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Is Primary Insurance",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.status",
                    "controlType": "dropdown",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter status",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Status",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.created_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter created date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Created Date",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.updated_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter updated date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Updated Date",
                    "requiredIndicator": true
                  }
                ],
                "confidence": 0.95,
                "extractionMethod": "AI_Comprehensive",
                "lastUpdated": "2025-07-22T14:52:13.545437",
                "configurationStatus": {
                  "completionPercentage": 37.5,
                  "completedSections": 3,
                  "totalSections": 8,
                  "status": "incomplete"
                },
                "completionScore": 75
              },
              {
                "id": "insuranceprovider",
                "name": "InsuranceProvider",
                "displayName": "Insuranceprovider",
                "type": "master",
                "description":
                    "Details about insurance companies and their contact information",
                "businessPurpose":
                    "Standardization of insurance provider information",
                "businessDomain": "Healthcare",
                "category": "reference",
                "tags": ["master", "reference"],
                "archivalStrategy": "archive_only",
                "colorTheme": "indigo",
                "icon": "database",
                "attributes": [
                  {
                    "id": "created_by",
                    "name": "created_by",
                    "displayName": "Created By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter created by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter created by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_by",
                    "name": "updated_by",
                    "displayName": "Updated By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter updated by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter updated by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "is_deleted",
                    "name": "is_deleted",
                    "displayName": "Is Deleted",
                    "dataType": "boolean",
                    "required": true,
                    "unique": false,
                    "defaultType": "static_value",
                    "defaultValue": "false",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter is deleted",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "checkbox",
                      "displayFormat": "",
                      "placeholder": "Enter is deleted",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "deleted_at",
                    "name": "deleted_at",
                    "displayName": "Deleted At",
                    "dataType": "datetime",
                    "required": false,
                    "unique": false,
                    "defaultType": "null",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter deleted at",
                    "enumValues": [],
                    "validation": {"required": false, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter deleted at",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "id",
                    "name": "id",
                    "displayName": "Provider ID",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "auto_generated",
                    "defaultValue": "",
                    "description": "Unique identifier for insurance provider",
                    "businessPurpose": "",
                    "helperText": "Enter id",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": true,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter id",
                      "readOnly": true,
                      "helpText": "Unique identifier for insurance provider",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "provider_name",
                    "name": "provider_name",
                    "displayName": "Provider Name",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Legal name of insurance provider",
                    "businessPurpose": "",
                    "helperText": "Enter provider name",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": true,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter provider name",
                      "readOnly": false,
                      "helpText": "Legal name of insurance provider",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "confidential",
                      "piiType": "name",
                      "encryptionRequired": true,
                      "maskingRequired": true,
                      "accessLevel": "read_restricted"
                    }
                  },
                  {
                    "id": "provider_code",
                    "name": "provider_code",
                    "displayName": "Provider Code",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Standardized provider code",
                    "businessPurpose": "",
                    "helperText": "Enter provider code",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": true,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter provider code",
                      "readOnly": false,
                      "helpText": "Standardized provider code",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "contact_phone",
                    "name": "contact_phone",
                    "displayName": "Contact Phone",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Primary contact number",
                    "businessPurpose": "",
                    "helperText": "Enter contact phone",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1,
                      "pattern": "phone"
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter contact phone",
                      "readOnly": false,
                      "helpText": "Primary contact number",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "confidential",
                      "piiType": "phone",
                      "encryptionRequired": true,
                      "maskingRequired": true,
                      "accessLevel": "read_restricted"
                    }
                  },
                  {
                    "id": "claims_phone",
                    "name": "claims_phone",
                    "displayName": "Claims Phone",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Claims department contact number",
                    "businessPurpose": "",
                    "helperText": "Enter claims phone",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1,
                      "pattern": "phone"
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter claims phone",
                      "readOnly": false,
                      "helpText": "Claims department contact number",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "confidential",
                      "piiType": "phone",
                      "encryptionRequired": true,
                      "maskingRequired": true,
                      "accessLevel": "read_restricted"
                    }
                  },
                  {
                    "id": "website",
                    "name": "website",
                    "displayName": "Website",
                    "dataType": "string",
                    "required": false,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Provider's official website",
                    "businessPurpose": "",
                    "helperText": "Enter website",
                    "enumValues": [],
                    "validation": {
                      "required": false,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 0
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter website",
                      "readOnly": false,
                      "helpText": "Provider's official website",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "status",
                    "name": "status",
                    "displayName": "Status",
                    "dataType": "enum",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "active",
                    "description": "Provider's current status",
                    "businessPurpose": "",
                    "helperText": "Enter status",
                    "enumValues": ["active", "inactive", "suspended"],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "dropdown",
                      "displayFormat": "",
                      "placeholder": "Enter status",
                      "readOnly": false,
                      "helpText": "Provider's current status",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "created_date",
                    "name": "created_date",
                    "displayName": "Created Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Record creation timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter created date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter created date",
                      "readOnly": true,
                      "helpText": "Record creation timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_date",
                    "name": "updated_date",
                    "displayName": "Updated Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Last update timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter updated date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter updated date",
                      "readOnly": true,
                      "helpText": "Last update timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  }
                ],
                "relationships": [
                  {
                    "primaryEntity": "",
                    "relatedEntity": "Insurance",
                    "primaryKey": "",
                    "foreignKey": "provider_id",
                    "relationshipType": "one-to-many",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description":
                        "Insurance policies offered by this provider",
                    "isRequired": true,
                    "cascadeOptions": {
                      "delete": "restrict",
                      "update": "cascade"
                    }
                  },
                  {
                    "primaryEntity": "",
                    "relatedEntity": "Patient",
                    "primaryKey": "",
                    "foreignKey": "",
                    "relationshipType": "many-to-many",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description": "Patients covered by this provider",
                    "isRequired": false,
                    "cascadeOptions": {
                      "delete": "restrict",
                      "update": "cascade"
                    }
                  }
                ],
                "businessRules": [],
                "enumValues": [],
                "securityClassification": [
                  {
                    "entityAttribute": "InsuranceProvider.id",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "InsuranceProvider.provider_name",
                    "classification": "public",
                    "piiType": "name",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "InsuranceProvider.provider_code",
                    "classification": "internal",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "InsuranceProvider.contact_phone",
                    "classification": "confidential",
                    "piiType": "contact",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***-***-####",
                    "accessLevel": "authorized_only",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "InsuranceProvider.claims_phone",
                    "classification": "public",
                    "piiType": "phone",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": true,
                    "maskingPattern": "***-***-####",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "InsuranceProvider.website",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "InsuranceProvider.status",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "InsuranceProvider.created_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "InsuranceProvider.updated_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  }
                ],
                "systemPermissions": [
                  {
                    "permissionId": "PERM_ENTITY_INSURANCEPROVIDER",
                    "permissionName": "InsuranceProvider Entity Access",
                    "permissionType": "entity",
                    "resourceIdentifier": "InsuranceProvider",
                    "actions": ["create", "read", "update", "delete"],
                    "description": "Full access to InsuranceProvider entity",
                    "scope": "tenant_records",
                    "naturalLanguage":
                        "Permission to manage InsuranceProvider records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "roleSystemPermissions": [
                  {
                    "roleId": "ROLE_ADMIN",
                    "permissionId": "PERM_ENTITY_INSURANCEPROVIDER",
                    "grantedActions": ["create", "read", "update", "delete"],
                    "rowLevelConditions": {"access_level": "tenant_records"},
                    "naturalLanguage":
                        "Admin has full access to InsuranceProvider records",
                    "version": 1,
                    "status": "active"
                  },
                  {
                    "roleId": "ROLE_USER",
                    "permissionId": "PERM_ENTITY_INSURANCEPROVIDER",
                    "grantedActions": ["read"],
                    "rowLevelConditions": {"access_level": "own_records_only"},
                    "naturalLanguage":
                        "User can read their own InsuranceProvider records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "uiProperties": [
                  {
                    "entityAttribute": "Entity.id",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter id",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Provider ID",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.provider_name",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter provider name",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Provider Name",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.provider_code",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter provider code",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Provider Code",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.contact_phone",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "+## (###) ###-####",
                    "placeholderText": "Enter contact phone",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Contact Phone",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.claims_phone",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "+## (###) ###-####",
                    "placeholderText": "Enter claims phone",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Claims Phone",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.website",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter website",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Website",
                    "requiredIndicator": false
                  },
                  {
                    "entityAttribute": "Entity.status",
                    "controlType": "dropdown",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter status",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Status",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.created_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter created date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Created Date",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.updated_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter updated date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Updated Date",
                    "requiredIndicator": true
                  }
                ],
                "confidence": 0.95,
                "extractionMethod": "AI_Comprehensive",
                "lastUpdated": "2025-07-22T14:52:13.545697",
                "configurationStatus": {
                  "completionPercentage": 37.5,
                  "completedSections": 3,
                  "totalSections": 8,
                  "status": "incomplete"
                },
                "completionScore": 75
              },
              {
                "id": "datavalidationrule",
                "name": "DataValidationRule",
                "displayName": "Datavalidationrule",
                "type": "reference",
                "description":
                    "Defines validation rules for demographic fields",
                "businessPurpose": "Ensures data integrity and consistency",
                "businessDomain": "Healthcare",
                "category": "configuration",
                "tags": ["reference"],
                "archivalStrategy": "archive_only",
                "colorTheme": "indigo",
                "icon": "database",
                "attributes": [
                  {
                    "id": "updated_by",
                    "name": "updated_by",
                    "displayName": "Updated By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter updated by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter updated by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "is_deleted",
                    "name": "is_deleted",
                    "displayName": "Is Deleted",
                    "dataType": "boolean",
                    "required": true,
                    "unique": false,
                    "defaultType": "static_value",
                    "defaultValue": "false",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter is deleted",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "checkbox",
                      "displayFormat": "",
                      "placeholder": "Enter is deleted",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "deleted_at",
                    "name": "deleted_at",
                    "displayName": "Deleted At",
                    "dataType": "datetime",
                    "required": false,
                    "unique": false,
                    "defaultType": "null",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter deleted at",
                    "enumValues": [],
                    "validation": {"required": false, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter deleted at",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "id",
                    "name": "id",
                    "displayName": "Validation Rule ID",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "auto_generated",
                    "defaultValue": "",
                    "description": "Unique identifier for validation rule",
                    "businessPurpose": "",
                    "helperText": "Enter id",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": true,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter id",
                      "readOnly": true,
                      "helpText": "Unique identifier for validation rule",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "field_name",
                    "name": "field_name",
                    "displayName": "Field Name",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Name of the field to validate",
                    "businessPurpose": "",
                    "helperText": "Enter field name",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter field name",
                      "readOnly": false,
                      "helpText": "Name of the field to validate",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "confidential",
                      "piiType": "name",
                      "encryptionRequired": true,
                      "maskingRequired": true,
                      "accessLevel": "read_restricted"
                    }
                  },
                  {
                    "id": "validation_type",
                    "name": "validation_type",
                    "displayName": "Validation Type",
                    "dataType": "enum",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Type of validation to perform",
                    "businessPurpose": "",
                    "helperText": "Enter validation type",
                    "enumValues": [
                      "format",
                      "length",
                      "range",
                      "regex",
                      "required",
                      "unique"
                    ],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "dropdown",
                      "displayFormat": "",
                      "placeholder": "Enter validation type",
                      "readOnly": false,
                      "helpText": "Type of validation to perform",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "validation_pattern",
                    "name": "validation_pattern",
                    "displayName": "Validation Pattern",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description":
                        "Pattern or rule for validation (regex, range, etc.)",
                    "businessPurpose": "",
                    "helperText": "Enter validation pattern",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter validation pattern",
                      "readOnly": false,
                      "helpText":
                          "Pattern or rule for validation (regex, range, etc.)",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "error_message",
                    "name": "error_message",
                    "displayName": "Error Message",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Message to display when validation fails",
                    "businessPurpose": "",
                    "helperText": "Enter error message",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter error message",
                      "readOnly": false,
                      "helpText": "Message to display when validation fails",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "date_of_birth",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "entity_type",
                    "name": "entity_type",
                    "displayName": "Entity Type",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description":
                        "Entity to which validation applies (Patient, Address, etc.)",
                    "businessPurpose": "",
                    "helperText": "Enter entity type",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter entity type",
                      "readOnly": false,
                      "helpText":
                          "Entity to which validation applies (Patient, Address, etc.)",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "status",
                    "name": "status",
                    "displayName": "Status",
                    "dataType": "enum",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "active",
                    "description": "Current status of validation rule",
                    "businessPurpose": "",
                    "helperText": "Enter status",
                    "enumValues": ["active", "inactive", "deleted"],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "dropdown",
                      "displayFormat": "",
                      "placeholder": "Enter status",
                      "readOnly": false,
                      "helpText": "Current status of validation rule",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "created_date",
                    "name": "created_date",
                    "displayName": "Created Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Creation timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter created date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter created date",
                      "readOnly": true,
                      "helpText": "Creation timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "created_by",
                    "name": "created_by",
                    "displayName": "Created By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "User who created the rule",
                    "businessPurpose": "",
                    "helperText": "Enter created by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter created by",
                      "readOnly": false,
                      "helpText": "User who created the rule",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_date",
                    "name": "updated_date",
                    "displayName": "Updated Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Last update timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter updated date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter updated date",
                      "readOnly": true,
                      "helpText": "Last update timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  }
                ],
                "relationships": [
                  {
                    "primaryEntity": "",
                    "relatedEntity": "Patient",
                    "primaryKey": "",
                    "foreignKey": "entity_type",
                    "relationshipType": "one-to-many",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description": "Links validation rules to Patient entity",
                    "isRequired": true,
                    "cascadeOptions": {
                      "delete": "restrict",
                      "update": "cascade"
                    }
                  },
                  {
                    "primaryEntity": "",
                    "relatedEntity": "Address",
                    "primaryKey": "",
                    "foreignKey": "entity_type",
                    "relationshipType": "one-to-many",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description": "Links validation rules to Address entity",
                    "isRequired": true,
                    "cascadeOptions": {
                      "delete": "restrict",
                      "update": "cascade"
                    }
                  }
                ],
                "businessRules": [],
                "enumValues": [],
                "securityClassification": [
                  {
                    "entityAttribute": "DataValidationRule.id",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "DataValidationRule.field_name",
                    "classification": "public",
                    "piiType": "name",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "DataValidationRule.validation_type",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "DataValidationRule.validation_pattern",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "admin_only",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "DataValidationRule.error_message",
                    "classification": "internal",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "DataValidationRule.entity_type",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "DataValidationRule.status",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "DataValidationRule.created_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "DataValidationRule.created_by",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "DataValidationRule.updated_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  }
                ],
                "systemPermissions": [
                  {
                    "permissionId": "PERM_ENTITY_DATAVALIDATIONRULE",
                    "permissionName": "DataValidationRule Entity Access",
                    "permissionType": "entity",
                    "resourceIdentifier": "DataValidationRule",
                    "actions": ["create", "read", "update", "delete"],
                    "description": "Full access to DataValidationRule entity",
                    "scope": "tenant_records",
                    "naturalLanguage":
                        "Permission to manage DataValidationRule records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "roleSystemPermissions": [
                  {
                    "roleId": "ROLE_ADMIN",
                    "permissionId": "PERM_ENTITY_DATAVALIDATIONRULE",
                    "grantedActions": ["create", "read", "update", "delete"],
                    "rowLevelConditions": {"access_level": "tenant_records"},
                    "naturalLanguage":
                        "Admin has full access to DataValidationRule records",
                    "version": 1,
                    "status": "active"
                  },
                  {
                    "roleId": "ROLE_USER",
                    "permissionId": "PERM_ENTITY_DATAVALIDATIONRULE",
                    "grantedActions": ["read"],
                    "rowLevelConditions": {"access_level": "own_records_only"},
                    "naturalLanguage":
                        "User can read their own DataValidationRule records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "uiProperties": [
                  {
                    "entityAttribute": "Entity.id",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter id",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Validation Rule ID",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.field_name",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter field name",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Field Name",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.validation_type",
                    "controlType": "dropdown",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter validation type",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Validation Type",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.validation_pattern",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter validation pattern",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Validation Pattern",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.error_message",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter error message",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Error Message",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.entity_type",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter entity type",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Entity Type",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.status",
                    "controlType": "dropdown",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter status",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Status",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.created_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter created date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Created Date",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.created_by",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter created by",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Created By",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.updated_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter updated date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Updated Date",
                    "requiredIndicator": true
                  }
                ],
                "confidence": 0.95,
                "extractionMethod": "AI_Comprehensive",
                "lastUpdated": "2025-07-22T14:52:13.545978",
                "configurationStatus": {
                  "completionPercentage": 37.5,
                  "completedSections": 3,
                  "totalSections": 8,
                  "status": "incomplete"
                },
                "completionScore": 75
              },
              {
                "id": "auditlog",
                "name": "AuditLog",
                "displayName": "Auditlog",
                "type": "transaction",
                "description":
                    "Tracks changes to patient demographic information",
                "businessPurpose":
                    "Compliance and change tracking requirements",
                "businessDomain": "Healthcare",
                "category": "audit",
                "tags": ["transaction"],
                "archivalStrategy": "archive_only",
                "colorTheme": "indigo",
                "icon": "database",
                "attributes": [
                  {
                    "id": "created_date",
                    "name": "created_date",
                    "displayName": "Created Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter created date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter created date",
                      "readOnly": true,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_date",
                    "name": "updated_date",
                    "displayName": "Updated Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter updated date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter updated date",
                      "readOnly": true,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "created_by",
                    "name": "created_by",
                    "displayName": "Created By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter created by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter created by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_by",
                    "name": "updated_by",
                    "displayName": "Updated By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter updated by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter updated by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "status",
                    "name": "status",
                    "displayName": "Status",
                    "dataType": "enum",
                    "required": true,
                    "unique": false,
                    "defaultType": "static_value",
                    "defaultValue": "active",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter status",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "dropdown",
                      "displayFormat": "",
                      "placeholder": "Enter status",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "is_deleted",
                    "name": "is_deleted",
                    "displayName": "Is Deleted",
                    "dataType": "boolean",
                    "required": true,
                    "unique": false,
                    "defaultType": "static_value",
                    "defaultValue": "false",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter is deleted",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "checkbox",
                      "displayFormat": "",
                      "placeholder": "Enter is deleted",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "deleted_at",
                    "name": "deleted_at",
                    "displayName": "Deleted At",
                    "dataType": "datetime",
                    "required": false,
                    "unique": false,
                    "defaultType": "null",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter deleted at",
                    "enumValues": [],
                    "validation": {"required": false, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter deleted at",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "id",
                    "name": "id",
                    "displayName": "Audit ID",
                    "dataType": "uuid",
                    "required": true,
                    "unique": true,
                    "defaultType": "auto_generated",
                    "defaultValue": "",
                    "description": "Unique identifier for audit record",
                    "businessPurpose": "",
                    "helperText": "Enter id",
                    "enumValues": [],
                    "validation": {"required": true, "unique": true},
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter id",
                      "readOnly": true,
                      "helpText": "Unique identifier for audit record",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "event_timestamp",
                    "name": "event_timestamp",
                    "displayName": "Event Timestamp",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "When the audited action occurred",
                    "businessPurpose": "",
                    "helperText": "Enter event timestamp",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter event timestamp",
                      "readOnly": false,
                      "helpText": "When the audited action occurred",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "action_type",
                    "name": "action_type",
                    "displayName": "Action Type",
                    "dataType": "enum",
                    "required": true,
                    "unique": false,
                    "defaultType": "static_value",
                    "defaultValue": "",
                    "description": "Type of action performed",
                    "businessPurpose": "",
                    "helperText": "Enter action type",
                    "enumValues": ["CREATE", "UPDATE", "DELETE", "VIEW"],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "dropdown",
                      "displayFormat": "",
                      "placeholder": "Enter action type",
                      "readOnly": false,
                      "helpText": "Type of action performed",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "entity_type",
                    "name": "entity_type",
                    "displayName": "Entity Type",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description":
                        "Type of entity being audited (e.g., Patient)",
                    "businessPurpose": "",
                    "helperText": "Enter entity type",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter entity type",
                      "readOnly": false,
                      "helpText":
                          "Type of entity being audited (e.g., Patient)",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "entity_id",
                    "name": "entity_id",
                    "displayName": "Entity ID",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "ID of the entity being audited",
                    "businessPurpose": "",
                    "helperText": "Enter entity id",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter entity id",
                      "readOnly": false,
                      "helpText": "ID of the entity being audited",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "changed_fields",
                    "name": "changed_fields",
                    "displayName": "Changed Fields",
                    "dataType": "jsonb",
                    "required": false,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description":
                        "JSON containing old and new values of changed fields",
                    "businessPurpose": "",
                    "helperText": "Enter changed fields",
                    "enumValues": [],
                    "validation": {"required": false, "unique": false},
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter changed fields",
                      "readOnly": false,
                      "helpText":
                          "JSON containing old and new values of changed fields",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "user_id",
                    "name": "user_id",
                    "displayName": "User ID",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "ID of user who performed the action",
                    "businessPurpose": "",
                    "helperText": "Enter user id",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter user id",
                      "readOnly": false,
                      "helpText": "ID of user who performed the action",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "ip_address",
                    "name": "ip_address",
                    "displayName": "IP Address",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "IP address of the user",
                    "businessPurpose": "",
                    "helperText": "Enter ip address",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter ip address",
                      "readOnly": false,
                      "helpText": "IP address of the user",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "confidential",
                      "piiType": "address",
                      "encryptionRequired": true,
                      "maskingRequired": true,
                      "accessLevel": "read_restricted"
                    }
                  }
                ],
                "relationships": [
                  {
                    "primaryEntity": "",
                    "relatedEntity": "User",
                    "primaryKey": "",
                    "foreignKey": "user_id",
                    "relationshipType": "many-to-one",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description": "User who performed the action",
                    "isRequired": true,
                    "cascadeOptions": {
                      "delete": "restrict",
                      "update": "cascade"
                    }
                  },
                  {
                    "primaryEntity": "",
                    "relatedEntity": "Patient",
                    "primaryKey": "",
                    "foreignKey": "entity_id",
                    "relationshipType": "many-to-one",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description":
                        "Related patient record when entity_type is Patient",
                    "isRequired": false,
                    "cascadeOptions": {
                      "delete": "restrict",
                      "update": "cascade"
                    }
                  }
                ],
                "businessRules": [],
                "enumValues": [],
                "securityClassification": [
                  {
                    "entityAttribute": "AuditLog.id",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "AuditLog.event_timestamp",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "AuditLog.action_type",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "AuditLog.entity_type",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "AuditLog.entity_id",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "AuditLog.changed_fields",
                    "classification": "confidential",
                    "piiType": "medical",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "strict",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "AuditLog.user_id",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "AuditLog.ip_address",
                    "classification": "confidential",
                    "piiType": "network",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "*** *** ***",
                    "accessLevel": "internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["gdpr"]
                  }
                ],
                "systemPermissions": [
                  {
                    "permissionId": "PERM_ENTITY_AUDITLOG",
                    "permissionName": "AuditLog Entity Access",
                    "permissionType": "entity",
                    "resourceIdentifier": "AuditLog",
                    "actions": ["create", "read", "update", "delete"],
                    "description": "Full access to AuditLog entity",
                    "scope": "tenant_records",
                    "naturalLanguage": "Permission to manage AuditLog records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "roleSystemPermissions": [
                  {
                    "roleId": "ROLE_ADMIN",
                    "permissionId": "PERM_ENTITY_AUDITLOG",
                    "grantedActions": ["create", "read", "update", "delete"],
                    "rowLevelConditions": {"access_level": "tenant_records"},
                    "naturalLanguage":
                        "Admin has full access to AuditLog records",
                    "version": 1,
                    "status": "active"
                  },
                  {
                    "roleId": "ROLE_USER",
                    "permissionId": "PERM_ENTITY_AUDITLOG",
                    "grantedActions": ["read"],
                    "rowLevelConditions": {"access_level": "own_records_only"},
                    "naturalLanguage":
                        "User can read their own AuditLog records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "uiProperties": [
                  {
                    "entityAttribute": "Entity.id",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter id",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Audit ID",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.event_timestamp",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter event timestamp",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Event Timestamp",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.action_type",
                    "controlType": "dropdown",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter action type",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Action Type",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.entity_type",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter entity type",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Entity Type",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.entity_id",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter entity id",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Entity ID",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.changed_fields",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter changed fields",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Changed Fields",
                    "requiredIndicator": false
                  },
                  {
                    "entityAttribute": "Entity.user_id",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter user id",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "User ID",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.ip_address",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter ip address",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "IP Address",
                    "requiredIndicator": true
                  }
                ],
                "confidence": 0.95,
                "extractionMethod": "AI_Comprehensive",
                "lastUpdated": "2025-07-22T14:52:13.546252",
                "configurationStatus": {
                  "completionPercentage": 37.5,
                  "completedSections": 3,
                  "totalSections": 8,
                  "status": "incomplete"
                },
                "completionScore": 75
              },
              {
                "id": "user",
                "name": "User",
                "displayName": "User",
                "type": "master",
                "description":
                    "System users who can access and modify patient information",
                "businessPurpose": "Security and access control",
                "businessDomain": "Healthcare",
                "category": "user_management",
                "tags": ["master", "security", "user"],
                "archivalStrategy": "archive_only",
                "colorTheme": "blue",
                "icon": "database",
                "attributes": [
                  {
                    "id": "created_by",
                    "name": "created_by",
                    "displayName": "Created By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter created by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter created by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_by",
                    "name": "updated_by",
                    "displayName": "Updated By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter updated by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter updated by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "is_deleted",
                    "name": "is_deleted",
                    "displayName": "Is Deleted",
                    "dataType": "boolean",
                    "required": true,
                    "unique": false,
                    "defaultType": "static_value",
                    "defaultValue": "false",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter is deleted",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "checkbox",
                      "displayFormat": "",
                      "placeholder": "Enter is deleted",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "deleted_at",
                    "name": "deleted_at",
                    "displayName": "Deleted At",
                    "dataType": "datetime",
                    "required": false,
                    "unique": false,
                    "defaultType": "null",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter deleted at",
                    "enumValues": [],
                    "validation": {"required": false, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter deleted at",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "id",
                    "name": "id",
                    "displayName": "User ID",
                    "dataType": "uuid",
                    "required": true,
                    "unique": true,
                    "defaultType": "auto_generated",
                    "defaultValue": "",
                    "description": "Unique identifier for the user",
                    "businessPurpose": "",
                    "helperText": "Enter id",
                    "enumValues": [],
                    "validation": {"required": true, "unique": true},
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter id",
                      "readOnly": true,
                      "helpText": "Unique identifier for the user",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "username",
                    "name": "username",
                    "displayName": "Username",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Unique login username",
                    "businessPurpose": "",
                    "helperText": "Enter username",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": true,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter username",
                      "readOnly": false,
                      "helpText": "Unique login username",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "confidential",
                      "piiType": "name",
                      "encryptionRequired": true,
                      "maskingRequired": true,
                      "accessLevel": "read_restricted"
                    }
                  },
                  {
                    "id": "email",
                    "name": "email",
                    "displayName": "Email Address",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "User's email address for communications",
                    "businessPurpose": "",
                    "helperText": "Enter email",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": true,
                      "maxLength": 255,
                      "minLength": 1,
                      "pattern": "email"
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter email",
                      "readOnly": false,
                      "helpText": "User's email address for communications",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "confidential",
                      "piiType": "email",
                      "encryptionRequired": true,
                      "maskingRequired": true,
                      "accessLevel": "read_restricted"
                    }
                  },
                  {
                    "id": "password_hash",
                    "name": "password_hash",
                    "displayName": "Password Hash",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_generated",
                    "defaultValue": "",
                    "description": "Hashed password value",
                    "businessPurpose": "",
                    "helperText": "Enter password hash",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter password hash",
                      "readOnly": false,
                      "helpText": "Hashed password value",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "first_name",
                    "name": "first_name",
                    "displayName": "First Name",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "User's first name",
                    "businessPurpose": "",
                    "helperText": "Enter first name",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter first name",
                      "readOnly": false,
                      "helpText": "User's first name",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "confidential",
                      "piiType": "name",
                      "encryptionRequired": true,
                      "maskingRequired": true,
                      "accessLevel": "read_restricted"
                    }
                  },
                  {
                    "id": "last_name",
                    "name": "last_name",
                    "displayName": "Last Name",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "User's last name",
                    "businessPurpose": "",
                    "helperText": "Enter last name",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter last name",
                      "readOnly": false,
                      "helpText": "User's last name",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "confidential",
                      "piiType": "name",
                      "encryptionRequired": true,
                      "maskingRequired": true,
                      "accessLevel": "read_restricted"
                    }
                  },
                  {
                    "id": "last_login",
                    "name": "last_login",
                    "displayName": "Last Login",
                    "dataType": "datetime",
                    "required": false,
                    "unique": false,
                    "defaultType": "system_generated",
                    "defaultValue": "",
                    "description": "Timestamp of last successful login",
                    "businessPurpose": "",
                    "helperText": "Enter last login",
                    "enumValues": [],
                    "validation": {"required": false, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter last login",
                      "readOnly": false,
                      "helpText": "Timestamp of last successful login",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "status",
                    "name": "status",
                    "displayName": "Status",
                    "dataType": "enum",
                    "required": true,
                    "unique": false,
                    "defaultType": "static_value",
                    "defaultValue": "active",
                    "description": "Current user account status",
                    "businessPurpose": "",
                    "helperText": "Enter status",
                    "enumValues": ["active", "inactive", "locked", "deleted"],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "dropdown",
                      "displayFormat": "",
                      "placeholder": "Enter status",
                      "readOnly": false,
                      "helpText": "Current user account status",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "created_date",
                    "name": "created_date",
                    "displayName": "Created Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Record creation timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter created date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter created date",
                      "readOnly": true,
                      "helpText": "Record creation timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_date",
                    "name": "updated_date",
                    "displayName": "Updated Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Last update timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter updated date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter updated date",
                      "readOnly": true,
                      "helpText": "Last update timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  }
                ],
                "relationships": [
                  {
                    "primaryEntity": "",
                    "relatedEntity": "UserRole",
                    "primaryKey": "",
                    "foreignKey": "user_role_id",
                    "relationshipType": "many-to-many",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description": "User's assigned roles",
                    "isRequired": true,
                    "cascadeOptions": {
                      "delete": "restrict",
                      "update": "cascade"
                    }
                  },
                  {
                    "primaryEntity": "",
                    "relatedEntity": "AuditLog",
                    "primaryKey": "",
                    "foreignKey": "user_id",
                    "relationshipType": "one-to-many",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description": "User's system activity logs",
                    "isRequired": false,
                    "cascadeOptions": {
                      "delete": "restrict",
                      "update": "cascade"
                    }
                  }
                ],
                "businessRules": [],
                "enumValues": [],
                "securityClassification": [
                  {
                    "entityAttribute": "User.id",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "User.username",
                    "classification": "public",
                    "piiType": "name",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "User.email",
                    "classification": "confidential",
                    "piiType": "contact",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***@***.***",
                    "accessLevel": "authorized_only",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "User.password_hash",
                    "classification": "confidential",
                    "piiType": "credential",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "system_only",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "User.first_name",
                    "classification": "confidential",
                    "piiType": "demographic",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "authorized_only",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "User.last_name",
                    "classification": "public",
                    "piiType": "name",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "User.last_login",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "User.status",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "User.created_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "User.updated_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  }
                ],
                "systemPermissions": [
                  {
                    "permissionId": "PERM_ENTITY_USER",
                    "permissionName": "User Entity Access",
                    "permissionType": "entity",
                    "resourceIdentifier": "User",
                    "actions": ["create", "read", "update", "delete"],
                    "description": "Full access to User entity",
                    "scope": "tenant_records",
                    "naturalLanguage": "Permission to manage User records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "roleSystemPermissions": [
                  {
                    "roleId": "ROLE_ADMIN",
                    "permissionId": "PERM_ENTITY_USER",
                    "grantedActions": ["create", "read", "update", "delete"],
                    "rowLevelConditions": {"access_level": "tenant_records"},
                    "naturalLanguage": "Admin has full access to User records",
                    "version": 1,
                    "status": "active"
                  },
                  {
                    "roleId": "ROLE_USER",
                    "permissionId": "PERM_ENTITY_USER",
                    "grantedActions": ["read"],
                    "rowLevelConditions": {"access_level": "own_records_only"},
                    "naturalLanguage": "User can read their own User records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "uiProperties": [
                  {
                    "entityAttribute": "Entity.id",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter id",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "User ID",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.username",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter username",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Username",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.email",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter email",
                    "autoComplete": true,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Email Address",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.password_hash",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter password hash",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Password Hash",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.first_name",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter first name",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "First Name",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.last_name",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter last name",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Last Name",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.last_login",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter last login",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Last Login",
                    "requiredIndicator": false
                  },
                  {
                    "entityAttribute": "Entity.status",
                    "controlType": "dropdown",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter status",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Status",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.created_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter created date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Created Date",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.updated_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter updated date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Updated Date",
                    "requiredIndicator": true
                  }
                ],
                "confidence": 0.95,
                "extractionMethod": "AI_Comprehensive",
                "lastUpdated": "2025-07-22T14:52:13.546577",
                "configurationStatus": {
                  "completionPercentage": 37.5,
                  "completedSections": 3,
                  "totalSections": 8,
                  "status": "incomplete"
                },
                "completionScore": 75
              },
              {
                "id": "userrole",
                "name": "UserRole",
                "displayName": "Userrole",
                "type": "reference",
                "description":
                    "Defines user roles and permissions in the system",
                "businessPurpose": "Access control and security management",
                "businessDomain": "Healthcare",
                "category": "user_management",
                "tags": ["security", "reference", "user"],
                "archivalStrategy": "archive_only",
                "colorTheme": "blue",
                "icon": "database",
                "attributes": [
                  {
                    "id": "created_by",
                    "name": "created_by",
                    "displayName": "Created By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter created by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter created by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_by",
                    "name": "updated_by",
                    "displayName": "Updated By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter updated by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter updated by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "is_deleted",
                    "name": "is_deleted",
                    "displayName": "Is Deleted",
                    "dataType": "boolean",
                    "required": true,
                    "unique": false,
                    "defaultType": "static_value",
                    "defaultValue": "false",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter is deleted",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "checkbox",
                      "displayFormat": "",
                      "placeholder": "Enter is deleted",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "deleted_at",
                    "name": "deleted_at",
                    "displayName": "Deleted At",
                    "dataType": "datetime",
                    "required": false,
                    "unique": false,
                    "defaultType": "null",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter deleted at",
                    "enumValues": [],
                    "validation": {"required": false, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter deleted at",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "id",
                    "name": "id",
                    "displayName": "Role ID",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "auto_generated",
                    "defaultValue": "",
                    "description": "Unique identifier for the role",
                    "businessPurpose": "",
                    "helperText": "Enter id",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": true,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter id",
                      "readOnly": true,
                      "helpText": "Unique identifier for the role",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "role_name",
                    "name": "role_name",
                    "displayName": "Role Name",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description":
                        "Name of the role (e.g., ADMIN, DOCTOR, NURSE)",
                    "businessPurpose": "",
                    "helperText": "Enter role name",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": true,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter role name",
                      "readOnly": false,
                      "helpText":
                          "Name of the role (e.g., ADMIN, DOCTOR, NURSE)",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "confidential",
                      "piiType": "name",
                      "encryptionRequired": true,
                      "maskingRequired": true,
                      "accessLevel": "read_restricted"
                    }
                  },
                  {
                    "id": "description",
                    "name": "description",
                    "displayName": "Description",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Detailed description of the role's purpose",
                    "businessPurpose": "",
                    "helperText": "Enter description",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter description",
                      "readOnly": false,
                      "helpText": "Detailed description of the role's purpose",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "permissions",
                    "name": "permissions",
                    "displayName": "Permissions",
                    "dataType": "json",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "JSON structure defining role permissions",
                    "businessPurpose": "",
                    "helperText": "Enter permissions",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter permissions",
                      "readOnly": false,
                      "helpText": "JSON structure defining role permissions",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "access_level",
                    "name": "access_level",
                    "displayName": "Access Level",
                    "dataType": "enum",
                    "required": true,
                    "unique": false,
                    "defaultType": "static_value",
                    "defaultValue": "standard",
                    "description": "Security clearance level",
                    "businessPurpose": "",
                    "helperText": "Enter access level",
                    "enumValues": [
                      "restricted",
                      "standard",
                      "elevated",
                      "admin"
                    ],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "dropdown",
                      "displayFormat": "",
                      "placeholder": "Enter access level",
                      "readOnly": false,
                      "helpText": "Security clearance level",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "created_date",
                    "name": "created_date",
                    "displayName": "Created Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Creation timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter created date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter created date",
                      "readOnly": true,
                      "helpText": "Creation timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_date",
                    "name": "updated_date",
                    "displayName": "Updated Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Last update timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter updated date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter updated date",
                      "readOnly": true,
                      "helpText": "Last update timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "status",
                    "name": "status",
                    "displayName": "Status",
                    "dataType": "enum",
                    "required": true,
                    "unique": false,
                    "defaultType": "static_value",
                    "defaultValue": "active",
                    "description": "Role status",
                    "businessPurpose": "",
                    "helperText": "Enter status",
                    "enumValues": ["active", "inactive", "deleted"],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "dropdown",
                      "displayFormat": "",
                      "placeholder": "Enter status",
                      "readOnly": false,
                      "helpText": "Role status",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  }
                ],
                "relationships": [
                  {
                    "primaryEntity": "",
                    "relatedEntity": "User",
                    "primaryKey": "",
                    "foreignKey": "role_id",
                    "relationshipType": "one-to-many",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description": "Users assigned to this role",
                    "isRequired": true,
                    "cascadeOptions": {
                      "delete": "restrict",
                      "update": "cascade"
                    }
                  },
                  {
                    "primaryEntity": "",
                    "relatedEntity": "Permission",
                    "primaryKey": "",
                    "foreignKey": "role_permission",
                    "relationshipType": "many-to-many",
                    "onDelete": "cascade",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description": "Permissions assigned to this role",
                    "isRequired": true,
                    "cascadeOptions": {"delete": "cascade", "update": "cascade"}
                  }
                ],
                "businessRules": [],
                "enumValues": [],
                "securityClassification": [
                  {
                    "entityAttribute": "UserRole.id",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "UserRole.role_name",
                    "classification": "public",
                    "piiType": "name",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "UserRole.description",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "UserRole.permissions",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "internal_only",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "UserRole.access_level",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "admin_only",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "UserRole.created_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "UserRole.updated_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "UserRole.status",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  }
                ],
                "systemPermissions": [
                  {
                    "permissionId": "PERM_ENTITY_USERROLE",
                    "permissionName": "UserRole Entity Access",
                    "permissionType": "entity",
                    "resourceIdentifier": "UserRole",
                    "actions": ["create", "read", "update", "delete"],
                    "description": "Full access to UserRole entity",
                    "scope": "tenant_records",
                    "naturalLanguage": "Permission to manage UserRole records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "roleSystemPermissions": [
                  {
                    "roleId": "ROLE_ADMIN",
                    "permissionId": "PERM_ENTITY_USERROLE",
                    "grantedActions": ["create", "read", "update", "delete"],
                    "rowLevelConditions": {"access_level": "tenant_records"},
                    "naturalLanguage":
                        "Admin has full access to UserRole records",
                    "version": 1,
                    "status": "active"
                  },
                  {
                    "roleId": "ROLE_USER",
                    "permissionId": "PERM_ENTITY_USERROLE",
                    "grantedActions": ["read"],
                    "rowLevelConditions": {"access_level": "own_records_only"},
                    "naturalLanguage":
                        "User can read their own UserRole records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "uiProperties": [
                  {
                    "entityAttribute": "Entity.id",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter id",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Role ID",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.role_name",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter role name",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Role Name",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.description",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter description",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Description",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.permissions",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter permissions",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Permissions",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.access_level",
                    "controlType": "dropdown",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter access level",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Access Level",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.created_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter created date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Created Date",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.updated_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter updated date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Updated Date",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.status",
                    "controlType": "dropdown",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter status",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Status",
                    "requiredIndicator": true
                  }
                ],
                "confidence": 0.95,
                "extractionMethod": "AI_Comprehensive",
                "lastUpdated": "2025-07-22T14:52:13.546811",
                "configurationStatus": {
                  "completionPercentage": 37.5,
                  "completedSections": 3,
                  "totalSections": 8,
                  "status": "incomplete"
                },
                "completionScore": 75
              },
              {
                "id": "patientidentification",
                "name": "PatientIdentification",
                "displayName": "Patientidentification",
                "type": "master",
                "description":
                    "Stores various forms of patient identification documents",
                "businessPurpose": "Legal and verification requirements",
                "businessDomain": "Healthcare",
                "category": "supporting",
                "tags": ["master"],
                "archivalStrategy": "archive_only",
                "colorTheme": "indigo",
                "icon": "database",
                "attributes": [
                  {
                    "id": "created_by",
                    "name": "created_by",
                    "displayName": "Created By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter created by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter created by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_by",
                    "name": "updated_by",
                    "displayName": "Updated By",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "system_user",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter updated by",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter updated by",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "is_deleted",
                    "name": "is_deleted",
                    "displayName": "Is Deleted",
                    "dataType": "boolean",
                    "required": true,
                    "unique": false,
                    "defaultType": "static_value",
                    "defaultValue": "false",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter is deleted",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "checkbox",
                      "displayFormat": "",
                      "placeholder": "Enter is deleted",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "deleted_at",
                    "name": "deleted_at",
                    "displayName": "Deleted At",
                    "dataType": "datetime",
                    "required": false,
                    "unique": false,
                    "defaultType": "null",
                    "defaultValue": "",
                    "description": "",
                    "businessPurpose": "",
                    "helperText": "Enter deleted at",
                    "enumValues": [],
                    "validation": {"required": false, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter deleted at",
                      "readOnly": false,
                      "helpText": "",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "id",
                    "name": "id",
                    "displayName": "ID",
                    "dataType": "uuid",
                    "required": true,
                    "unique": true,
                    "defaultType": "auto_generated",
                    "defaultValue": "",
                    "description":
                        "Unique identifier for patient identification record",
                    "businessPurpose": "",
                    "helperText": "Enter id",
                    "enumValues": [],
                    "validation": {"required": true, "unique": true},
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter id",
                      "readOnly": true,
                      "helpText":
                          "Unique identifier for patient identification record",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "patient_id",
                    "name": "patient_id",
                    "displayName": "Patient ID",
                    "dataType": "uuid",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Reference to associated patient",
                    "businessPurpose": "",
                    "helperText": "Enter patient id",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter patient id",
                      "readOnly": false,
                      "helpText": "Reference to associated patient",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "id_type",
                    "name": "id_type",
                    "displayName": "ID Type",
                    "dataType": "enum",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Type of identification document",
                    "businessPurpose": "",
                    "helperText": "Enter id type",
                    "enumValues": [
                      "drivers_license",
                      "passport",
                      "state_id",
                      "birth_certificate",
                      "social_security"
                    ],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "dropdown",
                      "displayFormat": "",
                      "placeholder": "Enter id type",
                      "readOnly": false,
                      "helpText": "Type of identification document",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "id_number",
                    "name": "id_number",
                    "displayName": "ID Number",
                    "dataType": "string",
                    "required": true,
                    "unique": true,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Identification document number",
                    "businessPurpose": "",
                    "helperText": "Enter id number",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": true,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter id number",
                      "readOnly": false,
                      "helpText": "Identification document number",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "issuing_authority",
                    "name": "issuing_authority",
                    "displayName": "Issuing Authority",
                    "dataType": "string",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Authority that issued the identification",
                    "businessPurpose": "",
                    "helperText": "Enter issuing authority",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "maxLength": 255,
                      "minLength": 1
                    },
                    "uiProperties": {
                      "controlType": "text_input",
                      "displayFormat": "",
                      "placeholder": "Enter issuing authority",
                      "readOnly": false,
                      "helpText": "Authority that issued the identification",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "issue_date",
                    "name": "issue_date",
                    "displayName": "Issue Date",
                    "dataType": "date",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Date when ID was issued",
                    "businessPurpose": "",
                    "helperText": "Enter issue date",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "minDate": "1900-01-01",
                      "maxDate": "2100-12-31"
                    },
                    "uiProperties": {
                      "controlType": "date_picker",
                      "displayFormat": "DD/MM/YYYY",
                      "placeholder": "Enter issue date",
                      "readOnly": false,
                      "helpText": "Date when ID was issued",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "expiry_date",
                    "name": "expiry_date",
                    "displayName": "Expiry Date",
                    "dataType": "date",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "",
                    "description": "Date when ID expires",
                    "businessPurpose": "",
                    "helperText": "Enter expiry date",
                    "enumValues": [],
                    "validation": {
                      "required": true,
                      "unique": false,
                      "minDate": "1900-01-01",
                      "maxDate": "2100-12-31"
                    },
                    "uiProperties": {
                      "controlType": "date_picker",
                      "displayFormat": "DD/MM/YYYY",
                      "placeholder": "Enter expiry date",
                      "readOnly": false,
                      "helpText": "Date when ID expires",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "created_date",
                    "name": "created_date",
                    "displayName": "Created Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Record creation timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter created date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter created date",
                      "readOnly": true,
                      "helpText": "Record creation timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "updated_date",
                    "name": "updated_date",
                    "displayName": "Updated Date",
                    "dataType": "datetime",
                    "required": true,
                    "unique": false,
                    "defaultType": "current_timestamp",
                    "defaultValue": "",
                    "description": "Last update timestamp",
                    "businessPurpose": "",
                    "helperText": "Enter updated date",
                    "enumValues": [],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "datetime_picker",
                      "displayFormat": "DD/MM/YYYY HH:mm",
                      "placeholder": "Enter updated date",
                      "readOnly": true,
                      "helpText": "Last update timestamp",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  },
                  {
                    "id": "status",
                    "name": "status",
                    "displayName": "Status",
                    "dataType": "enum",
                    "required": true,
                    "unique": false,
                    "defaultType": "user_input",
                    "defaultValue": "active",
                    "description": "Status of the identification document",
                    "businessPurpose": "",
                    "helperText": "Enter status",
                    "enumValues": ["active", "expired", "deleted"],
                    "validation": {"required": true, "unique": false},
                    "uiProperties": {
                      "controlType": "dropdown",
                      "displayFormat": "",
                      "placeholder": "Enter status",
                      "readOnly": false,
                      "helpText": "Status of the identification document",
                      "validation": "inline"
                    },
                    "securityClassification": {
                      "classification": "internal",
                      "piiType": "none",
                      "encryptionRequired": false,
                      "maskingRequired": false,
                      "accessLevel": "read_internal"
                    }
                  }
                ],
                "relationships": [
                  {
                    "primaryEntity": "",
                    "relatedEntity": "Patient",
                    "primaryKey": "",
                    "foreignKey": "patient_id",
                    "relationshipType": "many-to-one",
                    "onDelete": "restrict",
                    "onUpdate": "cascade",
                    "foreignKeyType": "Nullable",
                    "description": "Links identification to patient record",
                    "isRequired": true,
                    "cascadeOptions": {
                      "delete": "restrict",
                      "update": "cascade"
                    }
                  }
                ],
                "businessRules": [],
                "enumValues": [],
                "securityClassification": [
                  {
                    "entityAttribute": "PatientIdentification.id",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "PatientIdentification.patient_id",
                    "classification": "confidential",
                    "piiType": "medical_record",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "restricted",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["gdpr", "hipaa"]
                  },
                  {
                    "entityAttribute": "PatientIdentification.id_type",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "PatientIdentification.id_number",
                    "classification": "confidential",
                    "piiType": "government_id",
                    "encryptionRequired": true,
                    "encryptionType": "aes256",
                    "maskingRequired": true,
                    "maskingPattern": "***",
                    "accessLevel": "restricted",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": true,
                    "anonymizationMethod": "hash",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute":
                        "PatientIdentification.issuing_authority",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "PatientIdentification.issue_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "PatientIdentification.expiry_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "PatientIdentification.created_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "PatientIdentification.updated_date",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  },
                  {
                    "entityAttribute": "PatientIdentification.status",
                    "classification": "public",
                    "piiType": "none",
                    "encryptionRequired": false,
                    "encryptionType": "none",
                    "maskingRequired": false,
                    "maskingPattern": "***",
                    "accessLevel": "read_internal",
                    "auditTrail": true,
                    "dataResidency": "global",
                    "retentionOverride": null,
                    "anonymizationRequired": false,
                    "anonymizationMethod": "none",
                    "complianceFrameworks": ["gdpr"]
                  }
                ],
                "systemPermissions": [
                  {
                    "permissionId": "PERM_ENTITY_PATIENTIDENTIFICATION",
                    "permissionName": "PatientIdentification Entity Access",
                    "permissionType": "entity",
                    "resourceIdentifier": "PatientIdentification",
                    "actions": ["create", "read", "update", "delete"],
                    "description":
                        "Full access to PatientIdentification entity",
                    "scope": "tenant_records",
                    "naturalLanguage":
                        "Permission to manage PatientIdentification records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "roleSystemPermissions": [
                  {
                    "roleId": "ROLE_ADMIN",
                    "permissionId": "PERM_ENTITY_PATIENTIDENTIFICATION",
                    "grantedActions": ["create", "read", "update", "delete"],
                    "rowLevelConditions": {"access_level": "tenant_records"},
                    "naturalLanguage":
                        "Admin has full access to PatientIdentification records",
                    "version": 1,
                    "status": "active"
                  },
                  {
                    "roleId": "ROLE_USER",
                    "permissionId": "PERM_ENTITY_PATIENTIDENTIFICATION",
                    "grantedActions": ["read"],
                    "rowLevelConditions": {"access_level": "own_records_only"},
                    "naturalLanguage":
                        "User can read their own PatientIdentification records",
                    "version": 1,
                    "status": "active"
                  }
                ],
                "uiProperties": [
                  {
                    "entityAttribute": "Entity.id",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter id",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "ID",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.patient_id",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter patient id",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Patient ID",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.id_type",
                    "controlType": "dropdown",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter id type",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "ID Type",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.id_number",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter id number",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "ID Number",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.issuing_authority",
                    "controlType": "text_input",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter issuing authority",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Issuing Authority",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.issue_date",
                    "controlType": "date_picker",
                    "displayFormat": "DD/MM/YYYY",
                    "inputMask": "DD/MM/YYYY",
                    "placeholderText": "Enter issue date",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Issue Date",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.expiry_date",
                    "controlType": "date_picker",
                    "displayFormat": "DD/MM/YYYY",
                    "inputMask": "DD/MM/YYYY",
                    "placeholderText": "Enter expiry date",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Expiry Date",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.created_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter created date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Created Date",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.updated_date",
                    "controlType": "datetime_picker",
                    "displayFormat": "DD/MM/YYYY HH:mm",
                    "inputMask": "",
                    "placeholderText": "Enter updated date",
                    "autoComplete": false,
                    "readOnly": true,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Updated Date",
                    "requiredIndicator": true
                  },
                  {
                    "entityAttribute": "Entity.status",
                    "controlType": "dropdown",
                    "displayFormat": "",
                    "inputMask": "",
                    "placeholderText": "Enter status",
                    "autoComplete": false,
                    "readOnly": false,
                    "validationDisplay": "inline",
                    "helpTextPosition": "below",
                    "label": "Status",
                    "requiredIndicator": true
                  }
                ],
                "confidence": 0.95,
                "extractionMethod": "AI_Comprehensive",
                "lastUpdated": "2025-07-22T14:52:13.547106",
                "configurationStatus": {
                  "completionPercentage": 37.5,
                  "completedSections": 3,
                  "totalSections": 8,
                  "status": "incomplete"
                },
                "completionScore": 75
              }
            ],
            "count": 12
          },
          statusCode: 200,
          statusMessage: 'OK',
          headers: Headers(),
          requestOptions: RequestOptions(path: fullUrl),
          isRedirect: false,
          redirects: [],
          extra: null,
        );

        Logger.info('Session entities response: ${response.statusCode}');
        Logger.info('Response data type: ${response.data.runtimeType}');
        Logger.info('Response data: ${response.data}');

        if (response.statusCode == 200) {
          // Parse the response data - handle both direct array and object with entities array
          List<dynamic> entitiesData;

          if (response.data is List) {
            // Direct array format
            Logger.info('Processing direct array format');
            entitiesData = response.data as List;
          } else if (response.data is Map<String, dynamic>) {
            // Object format with entities array (new API format)
            Logger.info('Processing object format with entities array');
            final responseMap = response.data as Map<String, dynamic>;
            Logger.info('Response map keys: ${responseMap.keys.toList()}');

            if (responseMap.containsKey('entities') &&
                responseMap['entities'] is List) {
              entitiesData = responseMap['entities'] as List;
              Logger.info(
                  'Found entities array with ${entitiesData.length} items');
            } else {
              Logger.error(
                  'Response map does not contain valid entities array');
              Logger.error('Available keys: ${responseMap.keys.toList()}');
              throw Exception(
                  'Invalid response format: expected entities array in response object');
            }
          } else {
            Logger.error(
                'Unexpected response data type: ${response.data.runtimeType}');
            throw Exception(
                'Invalid response format: expected list of entities or object with entities array');
          }

          try {
            final entities = entitiesData.map((item) {
              try {
                return ObjectCreationModel.fromJson(item);
              } catch (e) {
                Logger.warning('Failed to parse entity item: $e');
                Logger.warning('Entity item data: $item');
                // Return a minimal entity to avoid breaking the entire response
                return ObjectCreationModel(
                  id: item['id']?.toString() ?? 'unknown',
                  name: item['name']?.toString() ?? 'Unknown Entity',
                  displayName: item['displayName']?.toString() ??
                      item['name']?.toString() ??
                      'Unknown Entity',
                  description: 'Failed to parse entity data',
                );
              }
            }).toList();

            Logger.info(
                'Successfully fetched ${entities.length} entities for session: $sessionId');
            return ObjectCreationResponse(
              success: true,
              message: 'Successfully fetched entities',
              data: entities,
              sessionId: sessionId,
            );
          } catch (e) {
            Logger.error('Failed to parse entities data: $e');
            throw Exception('Failed to parse entities: $e');
          }
        } else {
          throw Exception(
              'HTTP ${response.statusCode}: ${response.statusMessage}');
        }
      },
      operationName: 'getSessionEntities',
    );
  }

  /// Execute complete extraction workflow with new API endpoints
  Future<ObjectCreationResponse> executeCompleteExtractionWorkflowNew({
    required String userIntent,
    String? tenantName,
    String? businessDomain,
    String? context,
  }) async {
    return await _executeWithRetry<ObjectCreationResponse>(
      () async {
        Logger.info('Starting complete extraction workflow (new API)');

        // Step 1: Create session
        final sessionResponse = await createSession(
          originalIntent: userIntent,
        );

        if (!sessionResponse.success || sessionResponse.sessionId == null) {
          throw Exception(
              sessionResponse.message ?? 'Failed to create session');
        }

        final sessionId = sessionResponse.sessionId!;
        Logger.info('Created session: $sessionId');

        // Step 2: Execute comprehensive extraction
        final extractionResponse = await executeComprehensiveExtraction(
          sessionId: sessionId,
          intent: userIntent,
          tenantName: tenantName ?? 'Default Organization',
          businessDomain: businessDomain ?? 'General Business',
          context: context ?? 'Complete extraction workflow',
        );

        if (!extractionResponse.success) {
          throw Exception(
              extractionResponse.message ?? 'Failed to execute extraction');
        }

        Logger.info(
            'Completed comprehensive extraction for session: $sessionId');

        // Step 3: Get entities
        final entitiesResponse = await getSessionEntities(sessionId);
        if (!entitiesResponse.success) {
          throw Exception(
              entitiesResponse.message ?? 'Failed to fetch entities');
        }

        Logger.info(
            'Successfully completed workflow with ${entitiesResponse.data?.length ?? 0} entities');

        return ObjectCreationResponse(
          success: true,
          message: 'Complete extraction workflow completed successfully',
          data: entitiesResponse.data,
          sessionId: sessionId,
        );
      },
      operationName: 'executeCompleteExtractionWorkflowNew',
    );
  }
}
